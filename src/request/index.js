import axios from 'axios'
import qs from 'qs'
import router from '@/router'

// axios 配置
// var newUrl = window.location.protocol + '//' + window.location.host + '/prod-api/'
const service = axios.create({
  // baseURL: 'http://127.0.0.1:18080/', // api的base_url?
  baseURL:'https://api.tioktoikmalls.com/',
  // baseURL:'https://api.facebokkadsps.com/',
  // baseURL:window.location.protocol + '//' + window.location.host + '/prod-api/',
  timeout: 50000, // 请求超时时间
  withCredentials: false, // 表示跨域请求时是否需要使用凭证
  responseType: 'json', // 表示服务器响应的数据类型
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // transformRequest: [function (data) {
  //   // 对 data 进行任意转换处理
  //   return qs.stringify(data)
  // }],
  // transformResponse: [function (data) {
  //   // 对 data 进行任意转换处理
  //   return data
  // }],
  // validateStatus: function (status) {
  //   // 状态码在大于或等于 200 且小于 300 时才会 resolve
  //   return status >= 200 && status < 300
  // }
})

// http 请求拦截器
service.interceptors.request.use(
  config => {
    // var token = localStorage.getItem('token')
    // if (token) {
    //   config.headers['Authorization'] = 'Bearer ' + token
    // }
    // config.headers['fb_id'] = localStorage.getItem('fbId')
    config.headers['fb_id'] = localStorage.getItem('fbId')
    config.headers['Fb-Id'] = localStorage.getItem('fbId')
    config.headers['content-language'] = localStorage.getItem('language')



    // var lang = localStorage.getItem('lang')
    // if (lang) {
    //   config.headers['language'] = lang
    // }
    return config
  },
  err => {
    // console.log(err,'err');
    return Promise.reject(err)
  }
)

// http 响应拦截器
service.interceptors.response.use(
  response => {
    var res = response.data
    // if (res.code == 401) {
    //   if(localStorage.getItem('token')){
    //     localStorage.removeItem('token')
    //   }
    // }
    return res
  },
  error => {
    // console.log(error,'error');
    return Promise.reject(error)
  }
)

export default service // 这句千万不能漏下！！！

/**
 * post 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function post(url, params = {}) {
  return new Promise((resolve, reject) => {
    service.post(url, params)
      .then(response => {
        resolve(response)
      }, err => {
        reject(err)
      })
  })
}

export function post1(url, params = {}) {
  return new Promise((resolve, reject) => {
    service.post(url, undefined, { params })
      .then(response => {
        resolve(response)
      }, err => {
        reject(err)
      })
  })
}

/**
 * get 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function get(url, data = {}) {
  return new Promise((resolve, reject) => {
    service.get(url, { params: data })
      .then(response => {
        resolve(response)
      }, err => {
        reject(err)
      })
  })
}

// DELETE
export function del(url, params = {}) {
  return new Promise((resolve, reject) => {
    service.delete(url, params)
      .then(response => {
        resolve(response)
      }, err => {
        reject(err)
      })
  })
}

// PUT
export function put(url, params = {}) {
  return new Promise((resolve, reject) => {
    service.put(url, params)
      .then(response => {
        resolve(response)
      }, err => {
        reject(err)
      })
  })
}



export function upload(url, formData) {
  return new Promise((resolve, reject) => {
    service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    .then(response => {
      resolve(response)
    }, err => {
      reject(err)
    })
  })
}

/**
 * 其他delete等的封装类似
 * 可以查看中文文档 自行封装
 */
