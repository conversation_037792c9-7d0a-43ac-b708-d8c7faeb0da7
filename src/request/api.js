import { post, post1, get, del, put,upload} from '@/request/index'
import qs from 'qs'

// 计划管理列表
export function PlanList(options) {
  return get('facebook/userPlan/PlanList', options)
}
// 计划管理详情
export function PlanDetail(id) {
  return get(`facebook/userPlan/PlanDetail/${id}`, '')
}
// 计划管理投放
export function jihuaput(options) {
  return post('facebook/userPlan/put', options)
}
// 意见反馈
export function feedback(options) {
  return post('facebook/feedback/feedback', options)
}
// 广告中心 数据概括
export function dataStatistics(options) {
  return get('facebook/userPlan/dataStatistics', options)
}
// 用户信息
export function getUserInfo(options) {
  return get('facebook/user/getUserInfo', options)
}
// 计划跟随
export function follow(options) {
  return post('facebook/planFollow/follow', options)
}
// 计划跟随列表
export function followList(options) {
  return get('facebook/planFollow/followList', options)
}
// 我的钱包
export function property(options) {
  return get('facebook/user/property', options)
}
// 提现
export function withdraw(options) {
  return post('facebook/withdrawRecord/withdraw', options)
}
// 绑定交易密码
export function editInfo(options) {
  return post('facebook/user/editInfo', options)
}
// 是否已拥有新人优惠券
export function hasNewbieCoupon(options) {
  return get('facebook/userCoupon/hasNewbieCoupon', options)
}
// 领取新人优惠券
export function receiveNewbieCoupon(options) {
  return post('facebook/userCoupon/receiveNewbieCoupon', options)
}
// 优惠券列表
export function userCoupons(options) {
  return get('facebook/userCoupon/userCoupons', options)
}
// 资金明细
export function frontList(options) {
  return get('facebook/fundDetail/frontList', options)
}
// 激活用户
export function register(options) {
  return post('facebook/user/register', options)
}



// 用户是否已激活
export function isRegister(options) {
  return post('facebook/user/isRegister', options)
}

export function getVipLevel(options) {
  return get('facebook/vipLevel/frontList', options)
}

//vip充值
export function openVip(options) {
  return post('facebook/vipLevel/openVip', options)
}
//提交认证
export function authentication(options) {
  return post('system/authentication/authentication', options)
}

export function getAuthentication(options) {
  return post('system/authentication/getAuthentication', options)
}

export function getNotice(options) {
  return post('system/authentication/getNotice', options)
}




export function uploadImg(options) {
  return upload('facebook/user/upload', options)
}






// 全部游戏
export function getGameList(options) {
  return get('japi/user/game/getGameList', options)
}
export function getGameDetail(options) {
  return post('exapi/external-game/game/jiligames/startGame', options)
}
// vip等级列表
export function getAllDisplayVo(options) {
  return get('japi/user/vip/getAllDisplayVo', options)
}
// 
export function punchinconfi(options) {
  return get('japi/user/punch-in-config', options)
}
export function rechargeconfig(options) {
  return get('japi/user/recharge-config', options)
}





// 注册
export function videoregister(options) {
  return post('/auth/videoregister', options)
}
// 登录
export function videologin(options) {
  return post('/auth/videologin', options)
}
// 退出登录
export function authlogout(options) {
  return del('/auth/logout', options)
}
// 个人信息
export function userprofile(options) {
  return get('/system/user/profile', options)
}
// 搜索建议
export function searchSuggest(options) {
  return get('/youtube/video/searchSuggest', options)
}
// 视频列表
export function videoSarchlist(options) {
  return get('/youtube/video/sarch', options)
}
// 视频详情
export function videodetail(id) {
  return get(`/youtube/video/detail/${id}`, '')
}
// 添加观看历史记录
export function addVideohistory(options) {
  return post('/youtube/userVideoHistory/add?' + qs.stringify(options), '')
}
// 历史记录列表
export function userVideoHistory(options) {
  return get('/youtube/userVideoHistory/history', options)
}
// 清除历史记录
export function clearVideoHistory(options) {
  return del('/youtube/userVideoHistory/delete?type=' + options.type + '&ids=' + options.ids, '')
}
// 视频点赞
export function userVideoLike(options) {
  return post('/youtube/userVideoLike/like', options)
}
// 视频点赞列表
export function userVideoLikelist(options) {
  return get('/youtube/userVideoLike/list/simple', options)
}
// 清除点赞记录
export function clearVideoLike(options) {
  return del('/youtube/userVideoLike/delete?type=' + options.type + '&ids=' + options.ids, '')
}
// 侧边栏信息
export function websiteSidebaritem(options) {
  return get('/youtube/websiteSidebaritem/Sidebaritem', options)
}
// 视频详情侧边栏推荐
export function videorecommend(options) {
  return get('/youtube/video/recommend', options)
}
// 系统通知 公告
export function noticelist(options) {
  return get('/youtube/notice/list', options)
}
// 系统通知 公告详情
export function noticedetail(noticeId) {
  return get(`/youtube/notice/${noticeId}`, '')
}
// 修改密码
export function updatePwd(options) {
  return put('/system/user/profile/updatePwd?' + qs.stringify(options), '')
}
// 视频类别
export function categorylist(options) {
  return get('/youtube/videoCategory/categorylist', options)
}
// 帮助中心列表
export function helpDocumentList(options) {
  return get('/youtube/helpDocument/helpDocumentList', options)
}
// 帮助中心详情
export function helpDocumentInfo(documentId) {
  return get(`/youtube/helpDocument/helpDocumentInfo/${documentId}`, '')
}
// 广告
export function adList(options) {
  return get(`/youtube/ad/adList`, options)
}
// 系统配置
export function websiteconfig(options) {
  return get(`/youtube/website/config`, options)
}

// ==================== 佣金相关接口 ====================

/**
 * 获取佣金概览数据
 * @param {Object} options - 请求参数（通常为空对象）
 * @returns {Promise} 包含总佣金、今日佣金、本周佣金、本月佣金以及团队统计信息
 */
export function getCommissionOverview(options = {}) {
  return post('facebook/commission/overview', options)
}

/**
 * 获取团队成员列表
 * @param {Object} options - 请求参数
 * @param {number} options.level - 成员级别: 0-全部, 1-直属下级, 2-间接下级
 * @param {number} options.page - 页码，从1开始
 * @param {number} options.page_size - 每页数量，默认10
 * @returns {Promise} 包含团队成员列表和分页信息
 */
export function getTeamMembers(options) {
  return post('facebook/commission/team/members', options)
}

/**
 * 获取佣金明细列表
 * @param {Object} options - 请求参数
 * @param {number} options.level - 级别筛选: 0-全部, 1-直属下级, 2-间接下级
 * @param {number} options.page - 页码，从1开始
 * @param {number} options.page_size - 每页数量，默认10
 * @param {number|null} options.start_time - 开始时间戳（可选）
 * @param {number|null} options.end_time - 结束时间戳（可选）
 * @returns {Promise} 包含佣金明细列表和分页信息
 */
export function getCommissionDetails(options) {
  return post('facebook/commission/details', options)
}