<template>
  <div>
    <div class="placeholder_div"></div>
    <div class="publicheader" :style="{ transform: headerTransform }">
      <van-nav-bar :placeholder="true" :fixed="true">
        <template #left>
          <img
            src="../assets/logo.png"
            alt=""
            @click="getLinkHome"
            v-if="type == 0 && isDarkMode != 1"
          />
          <img
            src="../assets/logo1.png"
            alt=""
            @click="getLinkHome"
            v-if="type == 0 && isDarkMode == 1"
          />
          <i class="iconfont icon-yueliang DarkModeicon" v-if="homedrak == 1 && isDarkMode != 1" @click="getSheetEvery(1)"></i>
          <i class="iconfont icon-taiyang DarkModeicon" v-if="homedrak == 1 && isDarkMode == 1" @click="getSheetEvery(0)"></i>
          <div class="publicheader_left" v-if="type == 1 || type == 2">
            <i class="iconfont icon-zuojiantou-xi" @click="getGoback"></i>
            <span>{{headerName}}</span>
          </div>
        </template>
        <template #right>
          <div class="publicheader_right" v-if="type != 2">
            <van-icon name="search" @click="ShowSearch = true" />
            <i
              class="iconfont icon-wode"
              v-if="!userInfo.user"
              @click="getPublicLogin"
            ></i>
            <div class="public_avater" v-if="userInfo.user && type == 0" @click="getLinkUser">
              {{ userInfo.user.userName.substring(0, 2) }}
            </div>
            <i
              class="iconfont icon-gengduo-shuxiang"
              v-if="type == 1"
              @click="getOpenSheet"
            ></i>
          </div>
        </template>
      </van-nav-bar>
    </div>
    <div class="publicheader_searchmain" v-show="ShowSearch">
      <div class="publicheader_search">
        <i class="iconfont icon-zuojiantou-xi" @click="getShowSearch"></i>
        <div class="publicheader_searchinput">
          <input type="text" v-model="searchValue" @input="getSearchList" @keyup.enter="getLinkSearch(searchValue)" />
          <i
            class="van-icon van-icon-search"
            @click="getLinkSearch(searchValue)"
          ></i>
        </div>
      </div>
      <div class="publicheader_searchlist">
        <div
          class="publicheader_searcheverylist"
          v-for="(item, index) in searchContentList"
          :key="index"
        >
          <div class="publicheader_searcheverylist_title">
            <div
              class="publicheader_searcheverylist_titles"
              @click="getLinkSearch(item)"
            >
              {{ item }}
            </div>

            <!-- <div class="publicheader_searcheverylist_clear">
              移除
            </div> -->
          </div>
          <div
            class="publicheader_searcheverylist_copyicon"
            @click="searchValue = item"
          >
            <img src="../assets/searchcopy.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <van-overlay :show="ShowSearch" z-index="6" />
  </div>
</template>
<script>
import { userprofile, searchSuggest } from "@/request/api.js";
export default {
  name: "publicHeader",
  props: {
    homedrak: {
      type: Number,
      default: 0,
    },
    type: {
      type: Number,
      default: 0,
    },
    headerName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      userInfo: {},
      headerHidden: false,
      ShowSearch: false,
      searchValue: "",
      searchContentList: [],
      isDarkMode: 0,
    };
  },
  computed: {
    headerTransform() {
      return this.headerHidden ? "translateY(-100%)" : "translateY(0)";
    },
  },
  mounted() {
    this.isDarkMode = localStorage.getItem("isDarkMode");
    this.getUserInfo();
    this.$bus.$on("scrollBottom", (data) => {
      this.headerHidden = data;
    });
  },

  methods: {
    getSheetEvery(val) {
      localStorage.setItem("isDarkMode", val);
      window.location.reload();
    },
    getGoback() {
      this.$router.go(-1);
    },
    getOpenSheet() {
      this.$parent.getHeadersheet();
    },
    getLinkUser() {
      this.$router.push("/user");
    },
    getPublicLogin() {
      this.$router.push("/login");
    },
    getLinkHome() {
      this.$router.push("/");
    },
    getShowSearch() {
      this.searchValue = "";
      this.ShowSearch = false;
    },
    getLinkSearch(item) {
      this.ShowSearch = false;
      this.$router.push({
        path: "/search",
        query: {
          keyword: item,
        },
      });
    },
    // 获取用户信息
    getUserInfo() {
      userprofile().then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
        }
      });
    },
    // 搜索框提示
    getSearchList() {
      var params = {
        keyword: this.searchValue,
      };
      searchSuggest(params).then((res) => {
        this.searchContentList = res.data;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.placeholder_div {
  height: 48px;
}
.publicheader {
  box-shadow: 0 4px 2px -2px var(--boxshadow-color-primary);
  transition: transform 0.3s ease-in-out;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-color-primary);
  z-index: 4;
  .van-nav-bar__left {
    img {
      width: 90px;
      height: 20px;
    }
  }
  .DarkModeicon{
    font-size: 20px;
    margin-left: 16px;
    margin-top: 2px;
  }
  .publicheader_left {
    display: flex;
    align-items: center;
    margin-left: -10px;
    i {
      font-size: 30px;
      margin-right: 12px;
    }
    span {
      font-size: 14px;
    }
  }
  .publicheader_right {
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      font-size: 20px;
    }
    .icon-wode {
      margin-left: 22px;
    }
    .icon-gengduo-shuxiang {
      margin-left: 22px;
    }
    .public_avater {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #aa47bc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      cursor: pointer;
      color: #ffffff;
      margin-left: 22px;
    }
  }
}
.publicheader_searchmain {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 7;
  .publicheader_search {
    height: 46px;
    display: flex;
    align-items: center;
    background-color: var(--dialog-color-primary);
    i {
      font-size: 34px;
      margin-left: 8px;
    }
    .publicheader_searchinput {
      width: calc(100% - 60px);
      height: 30px;
      margin-right: 12px;
      margin-left: 8px;
      background-color: var(--tabbg-color-primary);
      border-radius: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      input {
        width: -webkit-fill-available;
        padding: 0 14px;
        height: 100%;
        border-radius: 40px;
        background-color: transparent;
        border: none;
        outline: none;
        color: var(--text-color-primary);
        font-size: 13px;
      }
      i {
        font-size: 22px;
        margin-right: 14px;
      }
    }
  }
  .publicheader_searchlist {
    max-height: 50vh;
    background: var(--bg-color-primary);
    overflow-y: scroll;
    overflow-x: hidden;
    .publicheader_searcheverylist {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .publicheader_searcheverylist_title {
        padding: 12px 3px 12px 8px;
        font-size: 0.3rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        border-bottom: 1px solid var(--tabbg-color-primary);
        .publicheader_searcheverylist_titles {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          flex: 1;
          text-align: left;
          font-size: 0.3rem;
          font-weight: 600;
        }
        .publicheader_searcheverylist_clear {
          color: var(--blue-color-primary);
          margin-left: 10px;
        }
      }
      .publicheader_searcheverylist_copyicon {
        background: var(--tabbg-color-primary);
        width: 37px;
        padding: 10px 0 11px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 14px;
        }
      }
    }
  }
}
</style>