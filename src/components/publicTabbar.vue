<template>
  <div>
    <div class="placeholdertabbar_div"></div>
    <div class="tabbar_div">
      <div
        class="tabbar_div_every"
        v-for="(item, index) in tabbarList"
        :key="index" @click="getTabberLink(item)">
        <!-- <img :src="item.icon" alt="" v-if="index != tabBar" />
        <img :src="item.activeIcon" alt="" v-else> -->
        <i class="iconfont" :class="item.icon" v-if="index != tabBar"></i>
        <i class="iconfont" :class="item.activeIcon" v-else></i>
        <div class="tabbar_div_name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script>
// 模板
export default {
  name: "publicTabbar",
  props: {
    tabBar: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      tabbarList: [
        {
          name: "首页",
          icon: 'icon-shouye',
          activeIcon: 'icon-shouye1',
          path: "/",
        },
        // {
        //   name: "Shorts",
        //   icon: require("@/assets/sidebarshorts.svg"),
        //   activeIcon: require("@/assets/sidebarshorts1.svg"),
        //   path: "/videolist",
        // },
        {
          name: "精品",
          icon: 'icon-subscribe',
          activeIcon: 'icon-subscribe1',
          path: "/recommend",
        },
        {
          name: "你",
          icon: 'icon-yous',
          activeIcon: 'icon-yous1',
          path: "/library",
        },
      ],
    };
  },
  computed: {},
  mounted() {},
  methods: {
    getTabberLink(item) {
      this.$router.push(item.path);
    },
  },
};
</script>
<style lang="less" scoped>
.placeholdertabbar_div{
  height: 48px;
}
.tabbar_div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 32px;
  background-color: var(--bg-color-primary);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-top: 1px solid var(--border-color-primary);
  .tabbar_div_every {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    i {
      font-size: 22px;
    }
    .tabbar_div_name {
      font-size: 10px;
      margin-top: 2px;
    }
  }
}
</style>