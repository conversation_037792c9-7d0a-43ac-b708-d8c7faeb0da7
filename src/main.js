import Vue from 'vue';
import 'amfe-flexible';

import App from './App.vue';
import i18n from './locales/index.js';
import router from './router';
import store from './store';
import axios from './request/index';
import './utils/index.js';
import moment from 'moment';
import 'animate.css';
import 'vant/lib/index.css';
import './assets/css/global.less';
import Vant from 'vant';
import LanguageUtils from './utils/language.js';

// 初始化语言设置
const currentLanguage = LanguageUtils.getCurrentLanguage();
LanguageUtils.setVantLanguage(currentLanguage);


Vue.prototype.$moment = moment;

Vue.config.productionTip = false
Vue.use(Vant);

// new Vconsole();
new Vue({
  router,
  i18n,
  store,
  axios,
  render: h => h(App),
  beforeCreate() {
    Vue.prototype.$bus = this
  }
}).$mount('#app')
