import Vue from "vue";
import VueI18n from "vue-i18n";
import zh from "./zh.js";
import zhTw from "./zh-tw.js";
import en from "./en.js";

Vue.use(VueI18n);

const DEFAULT_LANG = "zh_CN";
const LOCALE_KEY = "language";

const locales = {
  'zh_CN': {
    ...zh
  },
  'zh_TW': {
    ...zhTw
  },
  'en_US': {
    ...en
  },
};
if (!window.localStorage.getItem(LOCALE_KEY)) {
  window.localStorage.setItem(LOCALE_KEY, DEFAULT_LANG);
}
let langLocale = window.localStorage.getItem(LOCALE_KEY) ? window.localStorage.getItem(LOCALE_KEY)  : DEFAULT_LANG;
const i18n = new VueI18n({
  locale: langLocale,
  messages: locales,
  silentTranslationWarn: true
});

export default i18n;
