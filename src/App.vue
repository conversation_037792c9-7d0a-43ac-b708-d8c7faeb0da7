<template>
  <div id="app" class="no-zoom">
    <router-view></router-view>
  </div>
</template>
<script>
export default {
  name: "App",
  components: {},
  data() {
    return {

    };
  },
  created() { },
  watch: {

  },
  mounted() {

  },
  beforeD<PERSON>roy() {
  },
  computed: {

  },
  methods: {

  },
};
</script>
<style lang="less">
#app {
  font-family: "BlinkMacSystemFont, " Helvetica Neue", Helvetica, Segoe UI, Arial, Roboto, " PingFang SC", " miui", " Hiragino Sans GB", " Microsoft Yahei", sans-serif";
}

.container {
  .rotating {
    display: inline-block;
    animation-name: rotating;
    animation-duration: 1s;
    animation-iteration-count: 1;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
}
</style>
