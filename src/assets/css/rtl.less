/**
 * RTL (Right-to-Left) 布局样式
 * 专门为阿拉伯语等从右到左的语言设计
 */

/* 基础RTL样式 */
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

/* 文本对齐调整 */
[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: left !important;
}

/* Flex布局调整 */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

/* 外边距和内边距调整 */
[dir="rtl"] .ml-1 { margin-left: 0; margin-right: 4px; }
[dir="rtl"] .ml-2 { margin-left: 0; margin-right: 8px; }
[dir="rtl"] .ml-3 { margin-left: 0; margin-right: 12px; }
[dir="rtl"] .ml-4 { margin-left: 0; margin-right: 16px; }
[dir="rtl"] .ml-5 { margin-left: 0; margin-right: 20px; }

[dir="rtl"] .mr-1 { margin-right: 0; margin-left: 4px; }
[dir="rtl"] .mr-2 { margin-right: 0; margin-left: 8px; }
[dir="rtl"] .mr-3 { margin-right: 0; margin-left: 12px; }
[dir="rtl"] .mr-4 { margin-right: 0; margin-left: 16px; }
[dir="rtl"] .mr-5 { margin-right: 0; margin-left: 20px; }

[dir="rtl"] .pl-1 { padding-left: 0; padding-right: 4px; }
[dir="rtl"] .pl-2 { padding-left: 0; padding-right: 8px; }
[dir="rtl"] .pl-3 { padding-left: 0; padding-right: 12px; }
[dir="rtl"] .pl-4 { padding-left: 0; padding-right: 16px; }
[dir="rtl"] .pl-5 { padding-left: 0; padding-right: 20px; }

[dir="rtl"] .pr-1 { padding-right: 0; padding-left: 4px; }
[dir="rtl"] .pr-2 { padding-right: 0; padding-left: 8px; }
[dir="rtl"] .pr-3 { padding-right: 0; padding-left: 12px; }
[dir="rtl"] .pr-4 { padding-right: 0; padding-left: 16px; }
[dir="rtl"] .pr-5 { padding-right: 0; padding-left: 20px; }

/* 浮动调整 */
[dir="rtl"] .float-left {
  float: right;
}

[dir="rtl"] .float-right {
  float: left;
}

/* 定位调整 */
[dir="rtl"] .left-0 { left: auto; right: 0; }
[dir="rtl"] .right-0 { right: auto; left: 0; }

/* 边框调整 */
[dir="rtl"] .border-left { border-left: none; border-right: 1px solid; }
[dir="rtl"] .border-right { border-right: none; border-left: 1px solid; }

/* Vant组件RTL调整 */
[dir="rtl"] .van-nav-bar {
  .van-nav-bar__left {
    right: 16px;
    left: auto;
  }
  
  .van-nav-bar__right {
    left: 16px;
    right: auto;
  }
}

[dir="rtl"] .van-cell {
  .van-cell__left-icon {
    margin-right: 0;
    margin-left: 5px;
  }
  
  .van-cell__right-icon {
    margin-left: 0;
    margin-right: 5px;
  }
}

[dir="rtl"] .van-field {
  .van-field__left-icon {
    margin-right: 0;
    margin-left: 5px;
  }
  
  .van-field__right-icon {
    margin-left: 0;
    margin-right: 5px;
  }
}

[dir="rtl"] .van-button {
  .van-button__icon {
    margin-right: 0;
    margin-left: 4px;
  }
}

/* 表单元素RTL调整 */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="password"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="tel"],
[dir="rtl"] textarea {
  text-align: right;
  direction: rtl;
}

/* 图标方向调整 */
[dir="rtl"] .icon-arrow-left::before {
  transform: rotate(180deg);
}

[dir="rtl"] .icon-arrow-right::before {
  transform: rotate(180deg);
}

/* 自定义组件RTL调整 */
[dir="rtl"] .header-nav {
  .nav-left {
    right: 0;
    left: auto;
  }
  
  .nav-right {
    left: 0;
    right: auto;
  }
}

[dir="rtl"] .footer-nav {
  .nav-item {
    text-align: center;
  }
}

/* 列表项RTL调整 */
[dir="rtl"] .list-item {
  .item-icon {
    margin-right: 0;
    margin-left: 12px;
  }
  
  .item-arrow {
    transform: rotate(180deg);
  }
}

/* 卡片组件RTL调整 */
[dir="rtl"] .card-component {
  .card-header {
    text-align: right;
  }
  
  .card-actions {
    justify-content: flex-start;
  }
}

/* 阿拉伯语字体优化 */
[dir="rtl"] {
  font-family: 'Tahoma', 'Arial Unicode MS', 'Segoe UI', sans-serif;
  
  /* 针对阿拉伯语的行高和字距调整 */
  line-height: 1.6;
  letter-spacing: 0.5px;
}

/* 响应式RTL调整 */
@media (max-width: 768px) {
  [dir="rtl"] .mobile-nav {
    .nav-item {
      text-align: center;
    }
  }
}

/* 动画和过渡RTL调整 */
[dir="rtl"] .slide-enter-active,
[dir="rtl"] .slide-leave-active {
  transition: transform 0.3s ease;
}

[dir="rtl"] .slide-enter {
  transform: translateX(100%);
}

[dir="rtl"] .slide-leave-to {
  transform: translateX(-100%);
}

/* 特殊组件调整 */
[dir="rtl"] .van-popup--right {
  right: auto;
  left: 0;
}

[dir="rtl"] .van-popup--left {
  left: auto;
  right: 0;
}

[dir="rtl"] .van-sidebar {
  .van-sidebar-item {
    text-align: right;
  }
}

[dir="rtl"] .van-tabs {
  .van-tab {
    text-align: center;
  }
}

/* 数字和英文文本在阿拉伯语中的处理 */
[dir="rtl"] .number,
[dir="rtl"] .english-text {
  direction: ltr;
  display: inline-block;
}

/* 地址和URL在RTL中的特殊处理 */
[dir="rtl"] .url,
[dir="rtl"] .email,
[dir="rtl"] .address {
  direction: ltr;
  text-align: left;
}


