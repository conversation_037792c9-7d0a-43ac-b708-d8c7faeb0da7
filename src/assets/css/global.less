:root {
    --bg-color-primary: #ffffff; // 背景颜色
    --text-color-primary: #0f0f0f; // 文字颜色
    --tabbg-color-primary: rgba(0, 0, 0, .05); // tab背景颜色
    --tab-color-primary: #ffffff; // tab文字颜色
    --intro-color-primary: #606060; // intro文字颜色 checkbox文字颜色
    --blue-color-primary: #065fd4; // 蓝色按钮
    --bluebg-color-primary: #def1ff; // 蓝色按钮hover背景颜色
    --dialog-color-primary: #ffffff; //弹窗背景颜色
    --border-color-primary: rgba(0, 0, 0, .1); // 边框颜色
    --search-color-primary: #f8f8f8; // 搜索框背景颜色
    --search-border-color-primary: #d3d3d3; // 搜索框边框颜色
    --boxshadow-color-primary: rgba(0, 0, 0, .2); // boxshadow颜色
}

.dark {
    --bg-color-primary: #0f0f0f;
    --text-color-primary: #f1f1f1;
    --tabbg-color-primary: rgba(255, 255, 255, .1);
    --tab-color-primary: #0f0f0f;
    --intro-color-primary: #aaaaaa; // intro文字颜色 checkbox文字颜色
    --blue-color-primary: #3ea6ff; // 蓝色按钮
    --bluebg-color-primary: #263850; // 蓝色按钮hover背景颜色
    --dialog-color-primary: #282828; //弹窗背景颜色
    --border-color-primary: rgba(255, 255, 255, .2); // 边框颜色
    --search-color-primary: rgba(255, 255, 255, .08);
    --search-border-color-primary: #303030; // 搜索框边框颜色
    --boxshadow-color-primary: rgba(255, 255, 255, .2); // boxshadow颜色
}

.van-nav-bar {
    // background-color: transparent;
    color: var(--text-color-primary);
}

.van-cell {
    background-color: var(--bg-color-primary);
    color: var(--text-color-primary);
}

.van-cell--clickable:active {
    background-color: var(--tabbg-color-primary);
}

.van-cell::after {
    border-bottom-color: var(--border-color-primary);
}

.van-collapse-item__content {
    background-color: var(--bg-color-primary);
    color: var(--text-color-primary);
}

.van-collapse-item--border::after {
    border-top-color: var(--border-color-primary);
}

.van-popup {
    background-image: linear-gradient(to right, #26212c, #332b3b) !important;
    background-color: unset !important;
    background: unset !important;
}

.van-image__error,
.van-image__loading {
    background-color: radial-gradient(50% 50% at 50% 50%,
            #26212c 0,
            #1a161d 100%) !important;
}


.van-nav-bar {
    .van-nav-bar__title {
        font-weight: 600;
    }
    .van-icon{
        color: #000000;
        font-size: 5.12821vw;
        font-weight: 600;
    }
}
.van-tab--active{
    font-weight: 600;
}
.svg-icon{
    width: 1em;
    height: 1em;
    vertical-align: -.15em;
    fill: currentColor;
    overflow: hidden;
}

// 引入RTL（从右到左）布局样式支持
@import './rtl.less';