.popperclass {
    padding-left: 0 !important;
    padding-right: 0 !important;
    border-radius: 8px;
}
.v-modal{
    background: var(--dialog-color-primary) !important;
    opacity: .6;
}
.el-dialog {
    border-radius: 16px !important;
    background: var(--bg-color-primary) !important;
}

.el-dialog__body {
    color: var(--text-color-primary) !important;
}

.el-input__inner {
    color: var(--text-color-primary) !important;
    border: 1px solid #aeaeae;
    background-color: var(--bg-color-primary) !important;
}

.el-input__inner:focus {
    border: 2px solid var(--intro-color-primary);
}
.el-form-item.is-error .el-input__validateIcon{
    color: #F56C6C !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    border-color: var(--text-color-primary);
    background-color: var(--text-color-primary);
}

.el-checkbox__input.is-checked+.el-checkbox__label {
    color: var(--text-color-primary);
}

.el-checkbox__label {
    color: var(--intro-color-primary);
}
.el-checkbox__inner{
    border: 1px solid var(--intro-color-primary);
}
.el-checkbox__input.is-focus .el-checkbox__inner{
    border-color: var(--intro-color-primary);
}
.el-checkbox__inner::after{
    border-color: var(--bg-color-primary);
}