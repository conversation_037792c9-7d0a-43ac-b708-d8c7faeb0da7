import Vue from 'vue'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import VueRouter from 'vue-router'
import i18n from '@/locales/index'
NProgress.configure({ showSpinner: false }) // 关闭加载图标

Vue.use(VueRouter)
// 相同路由报错解决方案
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [
  {
    path: '/',
    redirect: '/entryCenter',
  },
  {
    path: '/entryCenter',
    name: 'entryCenter',
    component: () => import('../views/entryCenter.vue'),
    meta: {
      title: i18n.t('入口')
    }
  },
  {
    path: '/entryPlan',
    name: 'entryPlan',
    component: () => import('../views/entryPlan.vue'),
    meta: {
      title: i18n.t('入口')
    }
  },
  {
    path: '/plan',
    name: 'plan',
    component: () => import('../views/plan.vue'),
    meta: {
      title: i18n.t('计划管理')
    }
  },
  {
    path: '/planDetail',
    name: 'planDetail',
    component: () => import('../views/planDetail.vue'),
    meta: {
      title: i18n.t('计划详情')
    }
  },
  {
    path: '/goodsDetail',
    name: 'goodsDetail',
    component: () => import('../views/goodsDetail.vue'),
    meta: {
      title: i18n.t('产品详情')
    }
  },
  {
    path: '/advertiseCenter',
    name: 'advertiseCenter',
    component: () => import('../views/advertiseCenter.vue'),
    meta: {
      title: i18n.t('广告中心')
    }
  },
  {
    path: '/commission',
    name: 'commission',
    component: () => import('../views/commission.vue'),
    meta: {
      title: i18n.t('佣金中心')
    }
  },
  {
    path: '/vip',
    name: 'vip',
    component: () => import('../views/vip.vue'),
    meta: {
      title: i18n.t('vip特权')
    }
  },
  {
    path: '/Authentication',
    name: 'Authentication',
    component: () => import('../views/Authentication.vue'),
    meta: {
      title: i18n.t('实名认证')
    }
  },
  {
    path: '/wallet',
    name: 'wallet',
    component: () => import('../views/wallet.vue'),
    meta: {
      title: i18n.t('我的钱包')
    }
  },
  {
    path: '/setPassword',
    name: 'setPassword',
    component: () => import('../views/setPassword.vue'),
    meta: {
      title: i18n.t('设置交易密码')
    }
  },

  // {
  //   path: '/bindCard',
  //   name: 'bindCard',
  //   component: () => import('../views/bindCard.vue'),
  //   meta: {
  //     title: i18n.t('绑定银行卡')
  //   }
  // },

  {
    path: '/bindCard',
    name: 'bindCard',
    component: () => import('../views/bind-card.vue'),
    meta: {
      title: i18n.t('绑定银行卡')
    }
  },

  {
    path: '/foreignExchange',
    name: 'foreignExchange',
    component: () => import('../views/foreignExchange.vue'),
    meta: {
      title: i18n.t('外汇承兑')
    }
  },
  {
    path: '/customerService',
    name: 'customerService',
    component: () => import('../views/customerService.vue'),
    meta: {
      title: i18n.t('在线客服')
    }
  },
  {
    path: '/activities',
    name: 'activities',
    component: () => import('../views/activities.vue'),
    meta: {
      title: i18n.t('新人活动')
    }
  },
  {
    path: '/agentOperation',
    name: 'agentOperation',
    component: () => import('../views/agentOperation.vue'),
    meta: {
      title: i18n.t('计划跟随')
    }
  },
  {
    path: '/coupon',
    name: 'coupon',
    component: () => import('../views/coupon.vue'),
    meta: {
      title: i18n.t('优惠券')
    }
  },
  {
    path: '/feedback',
    name: 'feedback',
    component: () => import('../views/feedback.vue'),
    meta: {
      title: i18n.t('意见反馈')
    }
  },
  {
    path: '/questionDetail',
    name: 'questionDetail',
    component: () => import('../views/questionDetail.vue'),
    meta: {
      title: i18n.t('常见问题')
    }
  },

]

const router = new VueRouter({
  routes
})

router.afterEach((to, from) => {
  // 在每次路由跳转后滚动到最顶部
  window.scrollTo(0, 0);
});
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || 'Facebook'
  NProgress.inc() // 显示进度条
  next()
})

router.afterEach(() => {
  setTimeout(() => {
    NProgress.done() // 隐藏进度条
  }, 1000);
})

export default router
