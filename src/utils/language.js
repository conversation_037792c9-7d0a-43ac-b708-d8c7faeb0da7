import i18n from '@/locales/index.js'
import { Locale } from 'vant'
import enUS from 'vant/es/locale/lang/en-US'
import zhCN from 'vant/es/locale/lang/zh-CN'
import zhTW from 'vant/es/locale/lang/zh-TW'
// 注意：Vant目前不直接支持阿拉伯语，可能需要自定义或使用英语作为后备

/**
 * 语言工具类
 */
export class LanguageUtils {
  // 支持的语言列表
  static LANGUAGES = {
    ZH_CN: 'zh_CN',    // 简体中文
    ZH_TW: 'zh_TW',    // 繁体中文
    EN_US: 'en_US',    // 英文
    AR_SA: 'ar_SA'     // 阿拉伯语
  }

  // 语言显示名称
  static LANGUAGE_NAMES = {
    [LanguageUtils.LANGUAGES.ZH_CN]: '简体中文',
    [LanguageUtils.LANGUAGES.ZH_TW]: '繁體中文',
    [LanguageUtils.LANGUAGES.EN_US]: 'English',
    [LanguageUtils.LANGUAGES.AR_SA]: 'العربية'
  }

  // 默认语言
  static DEFAULT_LANGUAGE = LanguageUtils.LANGUAGES.ZH_CN

  // 本地存储键名
  static STORAGE_KEY = 'language'

  /**
   * 获取当前语言
   * @returns {string} 当前语言代码
   */
  static getCurrentLanguage() {
    return localStorage.getItem(LanguageUtils.STORAGE_KEY) || LanguageUtils.DEFAULT_LANGUAGE
  }

  /**
   * 设置语言
   * @param {string} language 语言代码
   */
  static setLanguage(language) {
    if (!Object.values(LanguageUtils.LANGUAGES).includes(language)) {
      console.warn(`不支持的语言: ${language}`)
      return false
    }

    // 保存到本地存储
    localStorage.setItem(LanguageUtils.STORAGE_KEY, language)
    
    // 设置 i18n 语言
    i18n.locale = language

    // 同时设置 Vant 组件库语言
    LanguageUtils.setVantLanguage(language)

    // 设置请求头语言
    // 这个会在下次请求时生效
    return true
  }

  /**
   * 设置 Vant 组件库语言
   * @param {string} language 语言代码
   */
  static setVantLanguage(language) {
    try {
      switch (language) {
        case LanguageUtils.LANGUAGES.ZH_TW:
          Locale.use('zh-TW', zhTW)
          break
        case LanguageUtils.LANGUAGES.EN_US:
          Locale.use('en-US', enUS)
          break
        case LanguageUtils.LANGUAGES.AR_SA:
          // Vant目前不支持阿拉伯语，使用英语作为后备
          Locale.use('en-US', enUS)
          break
        case LanguageUtils.LANGUAGES.ZH_CN:
        default:
          Locale.use('zh-CN', zhCN)
          break
      }
    } catch (error) {
      console.warn('设置 Vant 语言失败:', error)
    }
  }

  /**
   * 根据服务器返回的语言代码设置语言
   * @param {string} serverLang 服务器返回的语言代码 ('zh', 'en', 'ar')
   * @param {string} region 地区代码 ('CN', 'TW', 'US', 'SA' 等)
   */
  static setLanguageFromServer(serverLang, region = null) {
    let targetLanguage

    if (serverLang === 'zh') {
      // 中文，根据地区判断简繁体
      if (region === 'TW' || region === 'HK' || region === 'MO') {
        targetLanguage = LanguageUtils.LANGUAGES.ZH_TW
      } else {
        targetLanguage = LanguageUtils.LANGUAGES.ZH_CN
      }
    } else if (serverLang === 'en') {
      targetLanguage = LanguageUtils.LANGUAGES.EN_US
    } else if (serverLang === 'ar') {
      targetLanguage = LanguageUtils.LANGUAGES.AR_SA
    } else {
      // 未知语言，使用默认语言
      targetLanguage = LanguageUtils.DEFAULT_LANGUAGE
    }

    return LanguageUtils.setLanguage(targetLanguage)
  }

  /**
   * 获取所有支持的语言列表
   * @returns {Array} 语言列表
   */
  static getSupportedLanguages() {
    return Object.values(LanguageUtils.LANGUAGES).map(code => ({
      code,
      name: LanguageUtils.LANGUAGE_NAMES[code]
    }))
  }

  /**
   * 检查是否为中文（简体或繁体）
   * @param {string} language 语言代码
   * @returns {boolean}
   */
  static isChinese(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return lang === LanguageUtils.LANGUAGES.ZH_CN || lang === LanguageUtils.LANGUAGES.ZH_TW
  }

  /**
   * 检查是否为繁体中文
   * @param {string} language 语言代码
   * @returns {boolean}
   */
  static isTraditionalChinese(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return lang === LanguageUtils.LANGUAGES.ZH_TW
  }

  /**
   * 检查是否为简体中文
   * @param {string} language 语言代码
   * @returns {boolean}
   */
  static isSimplifiedChinese(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return lang === LanguageUtils.LANGUAGES.ZH_CN
  }

  /**
   * 检查是否为英文
   * @param {string} language 语言代码
   * @returns {boolean}
   */
  static isEnglish(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return lang === LanguageUtils.LANGUAGES.EN_US
  }

  /**
   * 检查是否为阿拉伯语
   * @param {string} language 语言代码
   * @returns {boolean}
   */
  static isArabic(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return lang === LanguageUtils.LANGUAGES.AR_SA
  }

  /**
   * 检查是否为RTL（从右到左）语言
   * @param {string} language 语言代码
   * @returns {boolean}
   */
  static isRTL(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return lang === LanguageUtils.LANGUAGES.AR_SA
  }

  /**
   * 获取语言显示名称
   * @param {string} language 语言代码
   * @returns {string} 语言显示名称
   */
  static getLanguageName(language = null) {
    const lang = language || LanguageUtils.getCurrentLanguage()
    return LanguageUtils.LANGUAGE_NAMES[lang] || lang
  }

  /**
   * 切换到下一个语言
   */
  static switchToNextLanguage() {
    const languages = Object.values(LanguageUtils.LANGUAGES)
    const currentLang = LanguageUtils.getCurrentLanguage()
    const currentIndex = languages.indexOf(currentLang)
    const nextIndex = (currentIndex + 1) % languages.length
    const nextLanguage = languages[nextIndex]
    
    return LanguageUtils.setLanguage(nextLanguage)
  }
}

// 导出默认实例
export default LanguageUtils
