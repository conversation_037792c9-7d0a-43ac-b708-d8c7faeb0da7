<template>
    <div class="container">
        <van-nav-bar :title="$t('绑定银行卡')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />
        <div class="container_nei">
            <div class="form-wrap">
                <div class="form-item">
                    <div class="label">{{$t('银行名称')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.bankName" :placeholder="$t('请输入银行名称')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('用户姓名')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.accountName" :placeholder="$t('请输入用户姓名')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('手机号')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.phoneNumber" :placeholder="$t('请输入手机号')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('银行账户')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.bankAccount" :placeholder="$t('请输入银行账户')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('Swift代码（非必填）')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.swiftCode" :placeholder="$t('请输入Swift代码')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('银行代码（非必填）')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.bankCode" :placeholder="$t('请输入银行代码')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('银行地址（非必填）')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.bankAddress" :placeholder="$t('请输入银行地址')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('收款人地址（非必填）')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.recipientAddress" :placeholder="$t('请输入收款人地址')" />
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">{{$t('邮编（非必填）')}}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.postalCode" :placeholder="$t('请输入邮编')" />
                    </div>
                </div>
            </div>
            <div class="btn-wrap">
                <van-button type="primary" color="#0866ff" size="large" @click="getBind">{{$t('确认绑定')}}</van-button>
            </div>
        </div>
        <van-popup v-model="show" position="bottom" @click-overlay="onClickLeft">
            <div class="dialog-wrap" :style="showKeyboard == true ? 'padding-bottom: 310px;' : ''">
                <div class="dialog-title">{{$t('交易密码')}}</div>
                <div class="desc">{{$t('请输入您的交易密码')}}</div>
                <van-password-input :value="password" :length="6" :focused="showKeyboard"
                    @focus="showKeyboard = true" />
                <div class="btn-wrap">
                    <van-button type="primary" color="#0866ff" size="large"
                        :disabled="password.length != 6 ? true : false" @click="getPassVerify">{{$t('确认')}}</van-button>
                </div>
                <van-number-keyboard v-model="password" :maxlength="6" :show="showKeyboard"
                    @blur="showKeyboard = false" />
            </div>
        </van-popup>
    </div>
</template>
<script>
import { getUserInfo, editInfo } from '@/request/api'
export default {
    data() {
        return {
            show: true,
            password: "",
            showKeyboard: true,
            value: "",
            form: {
                bankName: "",
                accountName: "",
                phoneNumber: "",
                bankAccount: "",
                swiftCode: "",
                bankCode: "",
                bankAddress: "",
                recipientAddress: "",
                postalCode: "",
            },
        };
    },
    mounted() {
        this.getUserDetail()
    },
    watch: {
        password(val) {
            if (val.length == 6) {
                this.showKeyboard = false
            }
        },
    },
    methods: {
        // 绑定银行卡
        getBind() {
            if(!this.form.bankName){
                this.$toast(this.$t('请输入银行名称'))
                return
            }
            if(!this.form.accountName){
                this.$toast(this.$t('请输入用户姓名'))
                return
            }
            if(!this.form.phoneNumber){
                this.$toast(this.$t('请输入手机号'))
                return
            }
            if(!this.form.bankAccount){
                this.$toast(this.$t('请输入银行账户'))
                return
            }
            editInfo(this.form).then((res) => {
                if (res.code == 200) {
                    this.$toast.success(this.$t('绑定成功'));
                    setTimeout(() => {
                        that.$router.go(-1)
                    }, 1000);
                }else{
                    this.$toast(res.msg)
                }
            });
        },
        // 用户信息
        getUserDetail() {
            const params = {};
            getUserInfo(params).then((res) => {
                if (res.code == 200) {
                    this.userInfo = res.data
                    this.form = this.userInfo
                }
            });
        },
        // 验证交易密码
        getPassVerify() {
            if (this.userInfo.password != this.password) {
                this.$toast(this.$t('交易密码错误'))
                return
            }
            this.show = false
        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .container_nei {
        display: flex;
        flex-direction: column;
        padding: 5.12821vw 4.10256vw;

        .form-wrap {
            flex: 1;

            .form-item {
                margin-bottom: 8.20513vw;

                .label {
                    font-size: 4.61538vw;
                    color: #000000;
                    margin-bottom: 5.12821vw;
                    line-height: 1;
                }

                .input-wrap {
                    padding-bottom: 8.20513vw;
                    border-bottom: 1px solid #E5E5E5;

                    /deep/ .van-field {
                        padding: 0;
                        font-size: 3.58974vw;
                        color: #000000;
                        line-height: 1;
                    }
                }
            }
        }

        .btn-wrap {
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);

            .van-button {
                width: 100%;
                border-radius: 16px;
            }
        }
    }

    .dialog-wrap {
        padding: 10.25641vw 4.10256vw 4.10256vw;
        background: #ffffff;
        transition: padding .3s;


        .dialog-title {
            text-align: center;
            font-weight: 700;
            font-size: 6.41026vw;
            margin-bottom: 12.82051vw;
        }

        .desc {
            text-align: center;
            font-size: 3.84615vw;
            color: #666666;
            margin-bottom: 7.69231vw;
        }

        .btn-wrap {
            margin-top: 10.25641vw;

            .van-button {
                width: 100%;
                border-radius: 16px;
            }
        }
    }
}
</style>