<template>
    <div class="container">
        <van-nav-bar :title="$t('新人活动')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />
        <img src="../assets/activities-banner.png" class="banner">
        <div class="content-wrap">
            <div class="title-wrap">
                <h1 class="title">{{$t('新人投放扶持')}}</h1>
                <h1 class="title">{{$t('领取展现量折扣券')}}</h1>
            </div>
            <van-button type="primary" color="#0866ff" size="large" @click="getReceiveCoupon"
                :disabled="isCoupon">{{$t('领取')}}</van-button>
        </div>
        <div class="activity-rules">
            <h2 class="title">{{$t('活动规则')}}</h2>
            <ul class="rules-list">
                <li>{{$t('activitiestext1')}}</li>
                <li>{{$t('activitiestext2')}}</li>
                <li>{{$t('activitiestext3')}}</li>
                <li>{{$t('activitiestext4')}}</li>
            </ul>
        </div>
    </div>
</template>
<script>
import { hasNewbieCoupon, receiveNewbieCoupon } from '@/request/api'
export default {
    data() {
        return {
            isCoupon: false
        };
    },
    mounted() {
        this.getIsCoupon()
    },
    methods: {
        // 检查是否领取新人优惠券
        getIsCoupon() {
            const params = {};
            hasNewbieCoupon(params).then((res) => {
                if (res.code == 200) {
                    this.isCoupon = res.data
                }
            });
        },
        // 领取新人优惠券
        getReceiveCoupon() {
            const params = {};
            receiveNewbieCoupon(params).then((res) => {
                if (res.code == 200) {
                    this.$toast.success(this.$t('领取成功'));
                    this.getIsCoupon()
                } else {
                    this.$toast(res.msg)
                }
            });

        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .banner {
        padding-top: 9.74359vw;
        width: 100%;
    }

    .content-wrap {
        padding: 0 4.10256vw 4.10256vw;

        .title-wrap {
            margin: 12.30769vw 0;

            .title {
                font-size: 6.41026vw;
                font-weight: 700;
                text-align: center;
            }
        }

        .van-button {
            border-radius: 16px;
        }
    }

    .activity-rules {
        border-top: 2.05128vw solid #F0F2F5;
        padding: 9.74359vw 4.10256vw;

        .title {
            font-size: 4.61538vw;
            color: #000000;
            font-weight: 500;
            margin-bottom: 5.64103vw;
        }

        .rules-list {
            list-style-type: disc;
            font-size: 3.58974vw;
            color: #65676a;
            padding-left: 4.10256vw;

            li {
                margin-bottom: 1.53846vw;
            }
        }
    }
}
</style>