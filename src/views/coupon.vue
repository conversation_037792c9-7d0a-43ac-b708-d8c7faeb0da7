<template>
    <div class="container">
        <van-nav-bar :title="$t('优惠券')" :border="false" placeholder fixed left-arrow @click-left="onClickLeft" z-index="2" />

        <div class="tab-wrap">
            <van-tabs title-inactive-color="#727073" title-active-color="#000000" color="#0866ff" line-height="3px"
                @click="getTab" line-width="70px" v-model="idx">
                <van-tab v-for="(item, index) in TabList" :title="item.name" :key="index">
                </van-tab>
            </van-tabs>
        </div>
        <div class="coupon-list">
            <van-list v-model="loading" :finished="finished" :loading-text="$t('加载中...')" :finished-text="$t('没有更多了')"
                @load="getList" offset="30">
                <div class="coupon-item used" v-for="(item, index) in couponsList" :key="index">
                    <div class="item-left" :class="item.status == 0 ? 'activebgblue' : ''">
                        <div class="coupon-type">{{$t('折扣券')}}</div>
                        <div class="tag" :class="item.status == 0 ? 'activecolorblue' : ''">{{ item.tag }}</div>
                    </div>
                    <div class="item-content">
                        <div class="title-line">
                            <div class="title" :class="item.status == 0 ? 'activecolorblack' : ''">{{ item.couponName }}
                            </div><span class="text" :class="item.status == 0 ? 'activecolorblue' : ''">{{ item.status ==
            0 ? $t('未使用') : $t('已使用') }}</span>
                        </div>
                        <div class="name" :class="item.status == 0 ? 'activecolorblack' : ''">{{ item.couponDesc }}</div>
                        <div class="time" :style="item.status == 0 ? 'opacity:.6' : ''">{{ item.expireTime}}{{$t('到期')}}</div>
                    </div>
                </div>
                <!-- <div class="coupon-item used">
                    <div class="item-left">
                        <div class="coupon-type">折扣券</div>
                        <div class="tag">新人投放扶持</div>
                    </div>
                    <div class="item-content">
                        <div class="title-line">
                            <div class="title">展现量投放</div><span class="text">已使用</span>
                        </div>
                        <div class="name">新账户扶持</div>
                        <div class="time">28/02/2025 15:11:53到期</div>
                    </div>
                </div> -->
            </van-list>
        </div>
    </div>
</template>
<script>
import { userCoupons } from '@/request/api'
export default {
    data() {
        return {
            idx: 0,
            page: 1,
            pageSize: 20,
            loading: false,
            finished: false,
            status: 0,
            couponsList: []
        };
    },
    mounted() {
        // this.getList()
    },
    computed:{
        TabList(){
            return [
                {
                    name: this.$t('全部')
                },
                {
                    name: this.$t('未使用')
                },
                {
                    name: this.$t('已使用')
                },
            ]
        }
    },
    methods: {
        getTab() {
            this.page = 1
            this.couponsList = []
            this.getList()
        },
        getList() {
            this.loading = true;
            const params = {
                pageNum: this.page++,
                pageSize: this.pageSize,
                status: this.idx == 0 ? "" : this.idx - 1,
            };
            userCoupons(params).then((res) => {
                if (res.code == 200) {
                    this.loading = false;
                    this.couponsList = this.couponsList.concat(res.rows);
                    if (this.couponsList.length >= res.total) {
                        this.finished = true;
                    }
                }
            });
        },
        onClickLeft() {
            this.$router.go(-1)
        }
    }
}
</script>
<style lang="less" scoped>
.container {
    .tab-wrap {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            width: 100%;
            height: 1px;
            background: #e5e5e5;
            bottom: 0;
            left: 0;
            right: 0;
        }
    }

    .coupon-list {
        flex: 1;
        overflow: hidden;
        padding: 5.89744vw 4.10256vw;

        .coupon-item {
            display: flex;
            align-items: center;
            height: 24.61538vw;
            margin-bottom: 3.84615vw;

            .item-left {
                background: rgb(173, 173, 173);
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                flex-shrink: 0;
                width: 30.76923vw;
                height: 100%;
                border-radius: 4.10256vw 0 0 4.10256vw;


                .coupon-type {
                    color: #ffffff;
                    font-size: 5.12821vw;
                    font-weight: 600;
                    line-height: 1;
                    margin-bottom: 2.05128vw;
                    text-align: center;
                }

                .tag {
                    color: #adadad;
                    padding: 1.02564vw 3.07692vw;
                    border-radius: 7.69231vw;
                    background: #ffffff;

                    font-size: 2.5641vw;
                    line-height: 1;
                }
            }

            .item-content {
                position: relative;
                display: flex;
                flex-direction: column;
                flex: 1;
                background: #EDF5FE;
                border-radius: 0 4.10256vw 4.10256vw 0;
                padding: 4.10256vw 6.15385vw 4.10256vw 5.12821vw;
                height: -webkit-fill-available;
                line-height: 1;

                &::after {
                    content: "";
                    position: absolute;
                    width: 6.66667vw;
                    height: 6.66667vw;
                    border-radius: 50%;
                    right: -3.33333vw;
                    top: 8.97436vw;
                    z-index: 1;
                    background: #ffffff;
                }

                .title-line {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 1.53846vw;

                    .title {
                        color: #8e8e8e;
                        font-size: 5.12821vw;
                        font-weight: 600;
                    }

                    .text {
                        color: #8e8e8e;
                        opacity: .8;
                        font-size: 2.5641vw;
                    }
                }

                .name {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 1.53846vw;
                    font-size: 4.10256vw;
                    color: #8e8e8e;
                }

                .time {
                    opacity: .2;
                    font-size: 2.5641vw;
                    color: #000000;
                    margin-top: 3.07692vw;
                }
            }

            .activecolorblue {
                color: #0866ff !important;
            }

            .activebgblue {
                background: #0866ff !important;
            }

            .activecolorblack {
                color: #000000 !important;
            }
        }
    }
}
</style>