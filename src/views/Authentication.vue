<template>
    <div class="container">
        <van-nav-bar :title="$t('实名认证')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />
        <div class="container_nei">
            <div class="tip" v-if="tipShow">{{$t('审核未通过')}} {{form.reason}}</div>
            <div class="form-wrap">
                <div class="form-item">
                    <div class="label">{{ $t('姓名') }}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.name" :disabled="isdisabled" :placeholder="$t('请输入') + $t('姓名')" />
                    </div>
                </div>

                <div class="form-item">
                    <div class="label">{{ $t('身份证号码') }}</div>
                    <div class="input-wrap">
                        <van-field v-model="form.idCar" :disabled="isdisabled" :placeholder="$t('请输入') + $t('身份证号码')" />
                    </div>
                </div>


              <div class="form-item">
                <div class="label">{{ $t('手机号') }}</div>
                <div class="input-wrap">
                  <van-field v-model="form.mobile" :disabled="isdisabled" :placeholder="$t('请输入') + $t('手机号')" />
                </div>
              </div>




              <div class="form-item">
                    <div class="label">{{ $t('身份证正面图片') }}</div>
                    <div class="input-wrap">
                        <van-uploader width="60vw" :disabled="isdisabled" v-model="form.frontImg" :after-read="afterCard"
                            :preview-image="false">
                            <van-image height="20vw" width="60vw" :src="form.frontImg" />
                        </van-uploader>

                    </div>
                </div>

                <div class="form-item">
                    <div class="label">{{ $t('身份证反面图片') }}</div>
                    <div class="input-wrap">
                        <van-uploader width="60vw" :disabled="isdisabled" v-model="form.oppositeImg" :after-read="afterCard2"
                            :preview-image="false">
                            <van-image height="20vw" width="60vw" :src="form.oppositeImg" />
                        </van-uploader>

                    </div>
                </div>



            </div>
            <div class="btn-wrap">
                <van-button type="primary" color="#0866ff" size="large"  v-if="form.status=='0'" disabled>{{ $t('审核中') }}</van-button>
                <van-button type="primary" color="#0866ff" size="large"  v-else-if="form.status=='1'" disabled>{{ $t('已认证') }}</van-button>
                <van-button type="primary" color="#0866ff" size="large" @click="getBind" v-else>{{ $t('提交认证') }}</van-button>
            </div>
        </div>

    </div>
</template>
<script>
import { getUserInfo, editInfo, uploadImg, authentication, getAuthentication } from '@/request/api'

export default {
    data() {
        return {
            show: false,
            password: "",
            showKeyboard: true,
            fileList: [],
            tipShow: false,
            userInfo: {},
            value: "",
            isdisabled:false,
            form: {
                name: "",
                idCar: "",
                mobile: "",
                frontImg: "",
                oppositeImg: "",
                status: "",

            },
        };
    },
    mounted() {
        this.getUserDetail()
    },
    watch: {
        password(val) {
            if (val.length == 6) {
                this.showKeyboard = false
            }
        },
    },
    methods: {
        // 绑定银行卡
        getBind() {
            let that = this;
            if (!this.form.name) {
                this.$toast(this.$t('请输入') + this.$t('姓名'))
                return
            }
            if (!this.form.idCar) {
                this.$toast(this.$t('请输入') + this.$t('身份证号码'))
                return
            }

          if (!this.form.mobile) {
            this.$toast(this.$t('请输入') + this.$t('手机号'))
            return
          }

            if (!this.form.frontImg) {
                this.$toast(this.$t('请上传正面图片'))
                return
            }
            if (!this.form.oppositeImg) {
                this.$toast(this.$t('请上传反面图片'))
                return
            }
            authentication(this.form).then((res) => {
                if (res.code == 200) {
                    this.$toast.success(this.$t('提交成功，等待审核'));
                    setTimeout(() => {
                        that.$router.go(-1)
                    }, 1000);
                } else {
                    this.$toast(res.msg)
                }
            });
        },
        // 用户信息
        getUserDetail() {
            const params = {};
            // getUserInfo(params).then((res) => {
            //     if (res.code == 200) {
            //         this.userInfo = res.data
            //     }
            // });
            getAuthentication(params).then((res) => {
                if (res.code == 200 && res.data) {
                    console.log(res, '9999999999');
                    this.form=res.data;
                    //状态（0.待审核  1.审核通过 2.审核不通过）
                    if(res.data.status=='0'||res.data.status=='1'){
                        this.isdisabled=true;
                    }
                    if(res.data.status=='2'){
                        this.tipShow=true;
                    }
                }
            });
        },
        // 验证交易密码
        getPassVerify() {
            if (this.userInfo.password != this.password) {
                this.$toast(this.$t('交易密码错误'))
                return
            }
            this.show = false
        },
        // 头像上传  文件上传完毕后会触发 after-read 回调函数，获取到对应的 file 对象。
        afterCard(file) {
            console.log(file)
            var formData = new FormData()
            formData.append('file', file.file)
            console.log(formData)
            let that = this;
            uploadImg(formData).then((res) => {
                if (res.code == 200) {
                    console.log(res);
                    that.form.frontImg = res.data.url
                }
            });

        },
        // 头像上传  文件上传完毕后会触发 after-read 回调函数，获取到对应的 file 对象。
        afterCard2(file) {
            console.log(file)
            var formData = new FormData()
            formData.append('file', file.file)
            console.log(formData)
            let that = this;
            uploadImg(formData).then((res) => {
                if (res.code == 200) {
                    console.log(res);
                    that.form.oppositeImg = res.data.url
                }
            });

        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .container_nei {
        display: flex;
        flex-direction: column;
        padding: 5.12821vw 4.10256vw;

        .form-wrap {
            flex: 1;

            .form-item {
                margin-bottom: 5.20513vw;

                .label {
                    font-size: 4.61538vw;
                    color: #000000;
                    margin-bottom: 5.12821vw;
                    line-height: 1;
                }

                .input-wrap {
                    padding-bottom: 5.20513vw;
                    border-bottom: 1px solid #E5E5E5;

                    /deep/ .van-field {
                        padding: 0;
                        font-size: 3.58974vw;
                        color: #000000;
                        line-height: 1;
                    }
                }
            }
        }

        .btn-wrap {
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);

            .van-button {
                width: 100%;
                border-radius: 16px;
            }
        }
    }

    .dialog-wrap {
        padding: 10.25641vw 4.10256vw 4.10256vw;
        background: #ffffff;
        transition: padding .3s;


        .dialog-title {
            text-align: center;
            font-weight: 700;
            font-size: 6.41026vw;
            margin-bottom: 12.82051vw;
        }

        .desc {
            text-align: center;
            font-size: 3.84615vw;
            color: #666666;
            margin-bottom: 7.69231vw;
        }

        .btn-wrap {
            margin-top: 10.25641vw;

            .van-button {
                width: 100%;
                border-radius: 16px;
            }
        }
    }

    .tip {
        color: #ffffff;
        text-align: center;
        font-size: 21px;
        line-height: 40px;
        background-color: #31c810;
        border-radius: 5px;
        height: 40px;
        margin-bottom: 14px;
    }
}
</style>
