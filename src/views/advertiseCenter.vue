<template>
  <div class="container">
    <van-nav-bar :title="$t('广告中心')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft"
                 z-index="9"/>
    <div class="person-wrap">
      <div class="person-content">
        <!-- <div class="van-image van-image--round avatar"><img
                src="https://z-p3-scontent.fpnh18-1.fna.fbcdn.net/v/t39.30808-1/480210579_122101950254765809_5320407286688672984_n.jpg?stp=dst-jpg_e15_fr_q75_tt6&amp;cstp=mx828x828&amp;ctp=s148x148&amp;_nc_cat=100&amp;ccb=1-7&amp;_nc_sid=e99d92&amp;_nc_ohc=2sPHPW2LgnoQ7kNvgErdAQO&amp;_nc_oc=AdigUJDrVcpPpmuOiyv22KerqIJLdZIU68fSzpme9nZST6dAPsmoHwp8XhVjsUqQQEo&amp;_nc_ad=z-m&amp;_nc_cid=1595&amp;_nc_zt=24&amp;_nc_ht=z-p3-scontent.fpnh18-1.fna&amp;oh=00_AYCa5rXF2d89C3bGQbbgPMsr5q9lmKc0ah-85YwgrPGvhw&amp;oe=67B64333"
                alt="avatar" class="van-image__img" style="object-fit: cover; object-position: center center;">
        </div> -->
        <van-image width="16.41026vw" round height="16.41026vw" :src="userInfo.fbAvatar"/>
        <div class="person-msg">
          <div class="person-name"><span class="name">{{ userInfo.fbNickname }}</span>
            <div @click="getLink('/vip')" class="vip-tag"><i
              class="van-badge__wrapper van-icon van-icon-diamond-o vip-icon"></i>
              <span class="vip-text">{{ userInfo.vip == 0 ? $t('立即开通') : 'VIP' + userInfo.vip }}</span>
              <i class="van-badge__wrapper van-icon van-icon-arrow arrow-icon"></i>
            </div>
          </div>
          <div class="person-desc">{{ $t('信誉分') }} {{ userInfo.creditScore }}</div>
          <div class="person-desc">{{ $t('店铺星级') }} ⭐</div>
          <div class="person-desc">{{ userInfo.showId }}</div>
        </div>
      </div>
    </div>
    <div class="balance-wrap">
      <div class="balance-item" @click="getLinkCommission">
        <div class="balance-icon">
          <img src="../assets/doller.png" alt="佣金"/>
        </div>
        <div class="balance-content">
          <div class="balance-label">{{ $t('佣金') }}</div>
          <div class="balance-amount">$ {{ commissionData.total || '0.00' }}</div>
        </div>
        <van-icon name="arrow" class="arrow-icon"/>
      </div>
      <div class="balance-item" @click="getLinkWallet">
        <div class="balance-icon">
          <img src="../assets/wallete.png" alt="钱包"/>
        </div>
        <div class="balance-content">
          <div class="balance-label">{{ $t('钱包') }}</div>
          <div class="balance-amount">$ {{ userInfo.availableFunds || '0.00' }}</div>
        </div>
        <van-icon name="arrow" class="arrow-icon"/>
      </div>
    </div>

    <div class="action-wrap">
      <div class="action-item" v-if="userInfo.invitationCode" @click="copyInvitationCode">
        <div class="action-icon">
          <img src="../assets/friend.png" alt="邀请码"/>
        </div>
        <div class="action-content">
          <div class="action-label">{{ $t('邀请码') }}</div>
          <div class="action-value">{{ userInfo.invitationCode }}</div>
        </div>
        <van-icon name="arrow" class="arrow-icon"/>
      </div>
      <div class="action-item" @click="getLink('customerService')">
        <div class="action-icon">
          <svg class="svg-icon" aria-hidden="true">
            <use xlink:href="#icon-device-message">
              <symbol fill="none" viewBox="0 0 24 24" id="icon-device-message">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M6.5 19.15c-3.34 0-5.25-1.91-5.25-5.25V8.5c0-3.34 1.91-5.25 5.25-5.25h2.7c.24 0 .47.12.61.31s.18.45.1.68c-.11.31-.16.65-.16 1.01v3.9c0 .76.23 1.39.67 ********** 1.07.67 ********** 0 .***********v1.2l2.73-1.83c.12-.08.27-.13.42-.13h2.6c.36 0 .7-.05 1.01-.16a.75.75 0 0 1 .68.1c.**********.31.61v1.7c0 3.35-1.91 5.26-5.25 5.26h-9ZM2.75 8.5v5.4c0 2.52 1.23 3.75 3.75 3.75h9c2.52 0 3.75-1.23 3.75-3.75v-.78c-.16.02-.33.03-.5.03h-2.37l-2.7 1.8c-.44.28-.99.31-1.44.06-.45-.24-.73-.71-.73-1.22v-.7c-.84-.13-1.58-.5-2.14-1.05-.71-.71-1.11-1.74-1.11-2.89v-3.9c0-.17.01-.34.02-.5H6.5c-2.52 0-3.75 1.23-3.75 3.75Z"
                      fill="currentColor"></path>
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M12.23 15.01c-.45-.24-.73-.71-.73-1.22v-.7c-.84-.13-1.58-.5-2.14-1.05-.71-.71-1.11-1.74-1.11-2.89v-3.9c0-.53.08-1.03.24-1.49.56-1.57 1.96-2.51 3.76-2.51h6.5c2.35 0 4 1.64 4 4v3.9c0 1.8-.93 3.2-2.5 3.76-.48.16-.98.24-1.5.24h-2.37l-2.7 1.8c-.24.16-.51.23-.78.23-.23 0-.46-.06-.67-.17ZM9.91 4.25c-.1.3-.16.64-.16 1v3.9c0 .76.23 1.39.67 ********** 1.07.67 ********** 0 .***********v1.2l2.73-1.83c.12-.08.27-.13.42-.13h2.6c.36 0 .7-.05 1.01-.16.95-.34 1.49-1.19 1.49-2.34v-3.9c0-1.54-.96-2.5-2.5-2.5h-6.5v.01c-1.15 0-2 .55-2.34 1.5Z"
                      fill="currentColor"></path>
                <path
                  d="M7.4 22.75c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h7.2c.41 0 .***********s-.34.75-.75.75H7.4Z"
                  fill="currentColor"></path>
                <path
                  d="M10.25 22v-3.6c0-.41.34-.75.75-.75s.***********V22c0 .41-.34.75-.75.75s-.75-.34-.75-.75ZM14.69 7.25c0-.41.34-.75.75-.75s.***********-.34.75-.75.75c-.42 0-.75-.34-.75-.75ZM17.5 7.25c0-.41.34-.75.75-.75s.***********-.34.75-.75.75c-.42 0-.75-.34-.75-.75ZM11.871 7.25c0-.41.34-.75.75-.75s.***********a.749.749 0 1 1-1.5 0Z"
                  fill="currentColor"></path>
              </symbol>
            </use>
          </svg>
        </div>
        <div class="action-content" >
          <div class="action-label">{{ $t('在线客服') }}</div>
        </div>
        <van-icon name="arrow" class="arrow-icon"/>
      </div>
    </div>

    <div class="banner-wrap" @click="getLink('activities')">
      <div class="banner"><img src="../assets/adcenter-banner.png">
        <div class="title-wrap">
          <div class="title">{{ $t('新人投放扶持') }}</div>
          <div class="desc">{{ $t('领取展现量折扣券') }}</div>
        </div>
      </div>
    </div>
    <div class="tab-wrap">
      <van-tabs title-inactive-color="#727073" title-active-color="#000000" color="#0866ff" line-height="3px"
                @click="getTab" line-width="70px" v-model="tabIdx">
        <van-tab v-for="(item, index) in timeList" :title="item.name" :key="index">
        </van-tab>
      </van-tabs>
    </div>
    <div class="section">
      <div class="section-title">
        <div class="title">{{ $t('数据概览') }}</div>
        <div class="desc">
          <span>{{ $t('数据每30s更新一次') }}</span>
          <svg class="svg-icon" aria-hidden="true" @click="getDataSummary" @animationend='inAnimation = false'
               :class="inAnimation ? 'rotating' : ''">
            <use xlink:href="#icon-refresh">
              <symbol fill="none" viewBox="0 0 13 13" id="icon-refresh">
                <path
                  d="M4.81 2.549c.436-.13.918-.216 1.45-.216a4.35 4.35 0 0 1 4.35 4.35 4.35 4.35 0 0 1-4.35 4.351 4.35 4.35 0 0 1-3.618-6.769M4.188 2.67l1.45-1.666M4.188 2.67l1.69 1.234"
                  stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
              </symbol>
            </use>
          </svg>
        </div>
      </div>
      <div class="data-section">
        <div class="data-item">
          <div class="number-line">
            <div class="number">$ {{ dataDetail.totalPutAmount }}</div>
          </div>
          <div class="data-title">{{ $t('投放金额') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-1">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-1">
                  <path
                    d="M8.672 14.33c0 1.29.99 2.33 2.22 2.33h2.51c1.07 0 1.94-.91 1.94-2.03 0-1.22-.53-1.65-1.32-1.93l-4.03-1.4c-.79-.28-1.32-.71-1.32-1.93 0-1.12.87-2.03 1.94-2.03h2.51c1.23 0 2.22 1.04 2.22 2.33M12 6v12"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path d="M15 22H9c-5 0-7-2-7-7V9c0-5 2-7 7-7h6c5 0 7 2 7 7v6c0 5-2 7-7 7Z"
                        stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">{{ dataDetail.totalPutOrder }}</div>
          </div>
          <div class="data-title">{{ $t('投放订单') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-2">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-2">
                  <path
                    d="M6.73 19.7c.82-.88 2.07-.81 2.79.15l1.01 1.35c.81 1.07 2.12 1.07 2.93 0l1.01-1.35c.72-.96 1.97-1.03 2.79-.15 1.78 1.9 3.23 1.27 3.23-1.39V7.04C20.5 3.01 19.56 2 15.78 2H8.22C4.44 2 3.5 3.01 3.5 7.04V18.3c0 2.67 1.46 3.29 3.23 1.4ZM8 7h8M9 11h6"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">$ {{ dataDetail.totalSpentAmount }}</div>
          </div>
          <div class="data-title">{{ $t('已消耗') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-3">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-3">
                  <path
                    d="M9.5 13.75c0 .97.75 1.75 1.67 1.75h1.88c.8 0 1.45-.68 1.45-1.53 0-.91-.4-1.24-.99-1.45l-3.01-1.05c-.59-.21-.99-.53-.99-1.45 0-.84.65-1.53 1.45-1.53h1.88c.92 0 1.67.78 1.67 1.75M12 7.5v9"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2" stroke="#292D32"
                        stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  <path d="M17 3v4h4M22 2l-5 5" stroke="#292D32" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">$ {{ dataDetail.totalWaitSpentAmount }}</div>
          </div>
          <div class="data-title">{{ $t('待消耗') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-4">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-4">
                  <path
                    d="M9.5 13.75c0 .97.75 1.75 1.67 1.75h1.88c.8 0 1.45-.68 1.45-1.53 0-.91-.4-1.24-.99-1.45l-3.01-1.05c-.59-.21-.99-.53-.99-1.45 0-.84.65-1.53 1.45-1.53h1.88c.92 0 1.67.78 1.67 1.75M12 7.5v9"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2M22 6V2h-4M17 7l5-5"
                        stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">{{ dataDetail.totalShowCount }}</div>
          </div>
          <div class="data-title">{{ $t('展示数') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-5">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-5">
                  <path
                    d="M9.03 14c-.39-.57-.61-1.26-.61-2 0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58-1.6 3.58-3.58 3.58"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path
                    d="M17.56 5.58c-1.69-1.2-3.59-1.85-5.56-1.85-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68 3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19"
                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">{{ dataDetail.totalClickCount }}</div>
          </div>
          <div class="data-title">{{ $t('点击数') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-6">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-6">
                  <path
                    d="m17.29 4.14-.07 3.79c-.01.52.32 1.21.74 1.52l2.48 1.88c1.59 1.2 1.33 2.67-.57 3.27l-3.23 1.01c-.54.17-1.11.76-1.25 1.31l-.77 2.94c-.61 2.32-2.13 2.55-3.39.51l-1.76-2.85c-.32-.52-1.08-.91-1.68-.88l-3.34.17c-2.39.12-3.07-1.26-1.51-3.08l1.98-2.3c.37-.43.54-1.23.37-1.77L4.28 6.43c-.59-1.9.47-2.95 2.36-2.33l2.95.97c.5.16 1.25.05 1.67-.26l3.08-2.22c1.66-1.2 2.99-.5 2.95 1.55ZM21.91 22l-3.03-3.03"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">$ {{ dataDetail.totalAdsIncome }}</div>
          </div>
          <div class="data-title">{{ $t('广告收入') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-7">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-7">
                  <path
                    d="M8 11.4c0 .77.6 1.4 1.33 1.4h1.5c.64 0 1.16-.55 1.16-1.22 0-.73-.32-.99-.79-1.16l-2.4-.84c-.48-.17-.8-.43-.8-1.16 0-.67.52-1.22 1.16-1.22h1.5c.74.01 1.34.63 1.34 1.4M10 12.85v.74M10 6.41v.78"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path
                    d="M9.99 17.98A7.99 7.99 0 1 0 9.99 2a7.99 7.99 0 0 0 0 15.98ZM12.98 19.88c.9 1.27 2.37 2.1 4.05 2.1 2.73 0 4.95-2.22 4.95-4.95 0-1.66-.82-3.13-2.07-4.03"
                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
        <div class="data-item">
          <div class="number-line">
            <div class="number">$ {{ dataDetail.totalGrossProfit }}</div>
          </div>
          <div class="data-title">{{ $t('利润') }}
            <svg class="svg-icon" aria-hidden="true">
              <use xlink:href="#icon-data-8">
                <symbol fill="none" viewBox="0 0 24 24" id="icon-data-8">
                  <path
                    d="M10.752 16.86v2.03c0 1.72-1.6 3.11-3.57 3.11-1.97 0-3.58-1.39-3.58-3.11v-2.03c0 1.72 1.6 2.94 3.58 2.94 1.97 0 3.57-1.23 3.57-2.94Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path
                    d="M10.75 14.11c0 .5-.14.96-.38 1.36-.59.97-1.8 1.58-3.2 1.58-1.4 0-2.61-.62-3.2-1.58-.24-.4-.38-.86-.38-1.36 0-.86.4-1.63 1.04-2.19.65-.57 1.54-.91 2.53-.91.99 0 1.88.35 2.53.91.66.55 1.06 1.33 1.06 2.19Z"
                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path
                    d="M10.752 14.11v2.75c0 1.72-1.6 2.94-3.57 2.94-1.97 0-3.58-1.23-3.58-2.94v-2.75c0-1.72 1.6-3.11 3.58-3.11.99 0 1.88.35 2.53.91.64.56 1.04 1.34 1.04 2.2ZM22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z"
                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                  <path
                    d="M2 10.5v-2c0-2.72 1.64-4.62 4.19-4.94.26-.04.53-.06.81-.06h9c.26 0 .51.01.75.05C19.33 3.85 21 5.76 21 8.5v1.45h-2.08c-.56 0-1.07.22-1.44.6-.42.41-.66 1-.6 1.63.09 1.08 1.08 1.87 2.16 1.87H21v1.45c0 3-2 5-5 5h-2.5"
                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round"></path>
                </symbol>
              </use>
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div class="tab-bar">
      <div class="tab-bar-item">
        <svg class="svg-icon" aria-hidden="true" @click="getLink('agentOperation')">
          <use xlink:href="#icon-folder-favorite">
            <symbol fill="none" viewBox="0 0 24 24" id="icon-folder-favorite">
              <path fill-rule="evenodd" clip-rule="evenodd"
                    d="m11.29 17.61-2.58-2.26c-.97-.85-1.1-2.31-.29-3.32a2.4 2.4 0 0 1 3.33-.42l.25.19.26-.2a2.4 2.4 0 0 1 3.33.42c.81 1.01.68 2.47-.29 3.32l-2.58 2.26a1.069 1.069 0 0 1-1.43.01Zm.71-1.37 2.31-2.02c.37-.32.42-.87.11-1.25a.903.903 0 0 0-1.26-.16l-.71.53c-.27.2-.63.2-.9 0l-.71-.53a.904.904 0 0 0-1.26.16c-.3.38-.25.93.11 1.25L12 16.24Z"
                    fill="#000"></path>
              <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M7 22.75c-4.41 0-5.75-1.34-5.75-5.75V7c0-4.41 1.34-5.75 5.75-5.75h1.5c1.75 0 2.3.57 3 1.5l1.5 2c.********** 1 .5h3c4.41 0 5.75 1.34 5.75 5.75v6c0 4.41-1.34 5.75-5.75 5.75H7ZM2.75 7v10c0 3.57.67 4.25 4.25 4.25h10c3.58 0 4.25-.68 4.25-4.25v-6c0-3.57-.67-4.25-4.25-4.25h-3c-1.28 0-1.7-.44-2.2-1.1l-1.5-2c-.52-.69-.68-.9-1.8-.9H7c-3.58 0-4.25.68-4.25 4.25Z"
                    fill="#000"></path>
            </symbol>
          </use>
        </svg>
        <span class="tab-text">{{ $t('代运营') }}</span>
      </div>
      <div class="tab-bar-item" @click="getLink('coupon')">
        <svg class="svg-icon" aria-hidden="true">
          <use xlink:href="#icon-discount-shape">
            <symbol fill="none" viewBox="0 0 24 24" id="icon-discount-shape">
              <path
                d="m3.989 14.66-1.52-1.52c-.62-.62-.62-1.64 0-2.26l1.52-1.52c.26-.26.47-.77.47-1.13V6.08c0-.88.72-1.6 1.6-1.6h2.15c.36 0 .87-.21 1.13-.47l1.52-1.52c.62-.62 1.64-.62 2.26 0l1.52 1.52c.26.26.77.47 1.13.47h2.15c.88 0 1.6.72 1.6 1.6v2.15c0 .36.21.87.47 1.13l1.52 1.52c.62.62.62 1.64 0 2.26l-1.52 1.52c-.26.26-.47.77-.47 1.13v2.15c0 .88-.72 1.6-1.6 1.6h-2.15c-.36 0-.87.21-1.13.47l-1.52 1.52c-.62.62-1.64.62-2.26 0l-1.52-1.52c-.26-.26-.77-.47-1.13-.47h-2.15c-.88 0-1.6-.72-1.6-1.6v-2.15c0-.37-.21-.88-.47-1.13ZM9 15l6-6M14.495 14.5h.009M9.495 9.5h.008"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
            </symbol>
          </use>
        </svg>
        <span class="tab-text">{{ $t('优惠券') }}</span>
      </div>
      <div class="tab-bar-item" @click="getLink('feedback')">
        <svg class="svg-icon" aria-hidden="true">
          <use xlink:href="#icon-edit">
            <symbol fill="none" viewBox="0 0 24 24" id="icon-edit">
              <path
                d="M9 22.75c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h2c.41 0 .***********s-.34.75-.75.75H9C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25v-2c0-.41.34-.75.75-.75s.***********v2c0 5.43-2.32 7.75-7.75 7.75H9Z"
                fill="#000"></path>
              <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.92 17.07c-.49-.49-.7-1.2-.59-1.95l.43-3.01c.08-.58.46-1.33.87-1.74l7.88-7.88c1.99-1.99 4.01-1.99 6 0 1.09 1.09 1.58 2.2 1.48 3.31-.09.9-.57 1.78-1.48 2.68l-7.88 7.88c-.41.41-1.16.79-1.74.87l-3.01.43c-.13.03-.26.03-.38.03-.61 0-1.17-.22-1.58-.62Zm1.77-5.64c-.19.19-.41.63-.45.89l-.43 3.01c-.***********.***********.39.21.68.17l3.01-.43c.26-.04.71-.26.89-.45l7.88-7.88c.65-.65.99-1.23 1.04-1.77.06-.65-.28-1.34-1.04-2.11-1.6-1.6-2.7-1.15-3.87.01l-7.88 7.88Z"
                    fill="#000"></path>
              <path
                d="M19.65 9.8a7.937 7.937 0 0 1-5.46-********** 0 0 1 .52-.93c.4-.***********.52.6 2.13 2.29 3.82 4.42 *********.63.53.52.93a.736.736 0 0 1-.92.52Z"
                fill="#000"></path>
            </symbol>
          </use>
        </svg>
        <span class="tab-text">{{ $t('意见反馈') }}</span>
      </div>
      <div class="tab-bar-item" @click="getLink('customerService')">
        <svg class="svg-icon" aria-hidden="true">
          <use xlink:href="#icon-device-message">
            <symbol fill="none" viewBox="0 0 24 24" id="icon-device-message">
              <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.5 19.15c-3.34 0-5.25-1.91-5.25-5.25V8.5c0-3.34 1.91-5.25 5.25-5.25h2.7c.24 0 .47.12.61.31s.18.45.1.68c-.11.31-.16.65-.16 1.01v3.9c0 .76.23 1.39.67 ********** 1.07.67 ********** 0 .***********v1.2l2.73-1.83c.12-.08.27-.13.42-.13h2.6c.36 0 .7-.05 1.01-.16a.75.75 0 0 1 .68.1c.**********.31.61v1.7c0 3.35-1.91 5.26-5.25 5.26h-9ZM2.75 8.5v5.4c0 2.52 1.23 3.75 3.75 3.75h9c2.52 0 3.75-1.23 3.75-3.75v-.78c-.16.02-.33.03-.5.03h-2.37l-2.7 1.8c-.44.28-.99.31-1.44.06-.45-.24-.73-.71-.73-1.22v-.7c-.84-.13-1.58-.5-2.14-1.05-.71-.71-1.11-1.74-1.11-2.89v-3.9c0-.17.01-.34.02-.5H6.5c-2.52 0-3.75 1.23-3.75 3.75Z"
                    fill="#000"></path>
              <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M12.23 15.01c-.45-.24-.73-.71-.73-1.22v-.7c-.84-.13-1.58-.5-2.14-1.05-.71-.71-1.11-1.74-1.11-2.89v-3.9c0-.53.08-1.03.24-1.49.56-1.57 1.96-2.51 3.76-2.51h6.5c2.35 0 4 1.64 4 4v3.9c0 1.8-.93 3.2-2.5 3.76-.48.16-.98.24-1.5.24h-2.37l-2.7 1.8c-.24.16-.51.23-.78.23-.23 0-.46-.06-.67-.17ZM9.91 4.25c-.1.3-.16.64-.16 1v3.9c0 .76.23 1.39.67 ********** 1.07.67 ********** 0 .***********v1.2l2.73-1.83c.12-.08.27-.13.42-.13h2.6c.36 0 .7-.05 1.01-.16.95-.34 1.49-1.19 1.49-2.34v-3.9c0-1.54-.96-2.5-2.5-2.5h-6.5v.01c-1.15 0-2 .55-2.34 1.5Z"
                    fill="#000"></path>
              <path
                d="M7.4 22.75c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h7.2c.41 0 .***********s-.34.75-.75.75H7.4Z"
                fill="#000"></path>
              <path
                d="M10.25 22v-3.6c0-.41.34-.75.75-.75s.***********V22c0 .41-.34.75-.75.75s-.75-.34-.75-.75ZM14.69 7.25c0-.41.34-.75.75-.75s.***********-.34.75-.75.75c-.42 0-.75-.34-.75-.75ZM17.5 7.25c0-.41.34-.75.75-.75s.***********-.34.75-.75.75c-.42 0-.75-.34-.75-.75ZM11.871 7.25c0-.41.34-.75.75-.75s.***********a.749.749 0 1 1-1.5 0Z"
                fill="#000"></path>
            </symbol>
          </use>
        </svg>
        <span class="tab-text">{{ $t('在线客服') }}</span>
      </div>
    </div>
    <div class="section">
      <div class="section-title">
        <div class="title">{{ $t('常见问题') }}</div>
      </div>
      <div class="question-list">
        <div class="question-item" v-for="(item, index) in questionList" :key="index"
             @click="getLinkquestion(index)">
          <div class="question">{{ item.title }}</div>
          <van-icon name="arrow"/>
        </div>
      </div>
    </div>


    <van-popup v-model="noticeShow" round style="width: 90%;">
      <div class="dialog-wrap">
        <div class="dialog-title" style="font-size: 4vw;font-weight: 400;margin-bottom: 2vw;">{{
            notice.noticeTitle
          }}
        </div>

        <div style="font-size: 3.5vw;" v-html="notice.noticeContent">

        </div>

        <van-button type="info" color="#0866ff" size="large" style="font-size: 4vw;" @click="noticeShow=false">
          {{ $t('确认') }}
        </van-button>
      </div>
    </van-popup>

    <!-- 充值弹窗 -->
    <van-popup v-model="rechargeShow" position="bottom" round>
      <div class="dialog-wrap">
        <div class="dialog-title">{{ $t('充值提示') }}</div>
        <img src="../assets/recharge-banner.png" alt="" width="343" class="banner">
        <p class="text">{{ $t('由于不同国家货币承兑问题') }}</p>
        <p class="text">{{ $t('当前只支持人工充值渠道') }}</p>
        <p class="text">{{ $t('请联系在线客服充值') }}</p>
        <van-button round type="info" color="#0866ff" size="large"
            @click="getLink('customerService')">{{ $t('联系客服') }}</van-button>
      </div>
    </van-popup>


  </div>
</template>
<script>
import { dataStatistics, getUserInfo, getNotice, getCommissionOverview } from '@/request/api'

export default {
  data () {
    return {
      tabIdx: 0,
      dataDetail: '',
      timer1: null,
      userInfo: '',
      inAnimation: true,
      notice: {},
      noticeShow: false,
      rechargeShow: false,
      commissionData: {
        total: '0.00'
      }
    }
  },
  mounted () {
    this.getDataSummary()
    this.timer1 = setInterval(() => {
      this.getDataSummary()
    }, 30000)
    this.getUserDetail()
    this.getNotices()
    this.getCommissionData()
  },
  beforeUnmount () {

  },
  computed: {
    timeList () {
      return [
        {
          name: this.$t('本日')
        },
        {
          name: this.$t('本周')
        },
        {
          name: this.$t('本月')
        },
        {
          name: this.$t('所有')
        },
      ]
    },
    questionList () {
      return [
        {
          title: this.$t('什么是Facebook广告联盟？')
        },
        {
          title: this.$t('平台收益与会员推荐权益')
        },
        {
          title: this.$t('充值与提现')
        },
        {
          title: this.$t('推广分润奖励')
        },
        {
          title: this.$t('会员推荐机制')
        },
        {
          title: this.$t('风险提示及合规说明')
        },
      ]
    }
  },
  beforeDestroy () {
    if (this.timer1) {
      clearInterval(this.timer1)
    }
    this.timer1 = null
  },
  methods: {
    // 点击每周 月tab
    getTab () {
      this.getDataSummary()
    },

    getNotices () {
      let that = this
      let index = window.localStorage.getItem('index')
      console.log(index, '88558') // 上一个页面地址
      if (index == 'index') {
        getNotice({}).then((res) => {
          if (res.code == 200 && res.data) {
            that.notice = res.data
            that.noticeShow = true
            window.localStorage.setItem('index', 'no')
          }
        })
      }
    },

    // 数据概括
    getDataSummary () {
      const params = {
        type: this.tabIdx + 1
      }
      dataStatistics(params).then((res) => {
        if (res.code == 200) {
          this.dataDetail = res.data
          this.inAnimation = true
        }
      })
    },
    // 用户信息
    getUserDetail () {
      const params = {}
      getUserInfo(params).then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data
        }
      })
    },
    // 获取佣金数据
    getCommissionData () {
      const params = {}
      getCommissionOverview(params).then((res) => {
        if (res.code == 200 && res.data && res.data.commission) {
          this.commissionData = res.data.commission
        }
      }).catch((error) => {
        console.error('获取佣金数据失败:', error)
      })
    },
    getLinkquestion (index) {
      this.$router.push({
        path: '/questionDetail',
        query: {
          index: index
        }
      })
    },
    getLinkWallet () {
      this.$router.push({
        path: '/wallet'
      })
    },
    getLinkCommission () {
      this.$router.push({
        path: '/commission'
      })
    },
    onClickLeft () {
      window.location.href = 'hybrid://doAction?params=close'
    },
    getLink (val) {
      this.$router.push({
        path: val
      })
    },
    copyInvitationCode () {
      const code = this.userInfo.invitationCode
      if (!code) return

      // 优先使用 clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(code).then(() => {
          this.$toast.success(this.$t('复制成功'))
        }).catch(() => {
          this.$toast.fail(this.$t('复制失败'))
        })
      } else {
        // 兼容老浏览器或 WebView
        const input = document.createElement('input')
        input.value = code
        document.body.appendChild(input)
        input.select()
        try {
          const result = document.execCommand('copy')
          if (result) {
            this.$toast.success(this.$t('复制成功'))
          } else {
            this.$toast.fail(this.$t('复制失败'))
          }
        } catch (e) {
          this.$toast.fail(this.$t('复制失败'))
        }
        document.body.removeChild(input)
      }
    },
  },
}
</script>
<style lang="less" scoped>
.container {
  .person-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4.61538vw 4.10256vw;

    .person-content {
      display: flex;
      align-items: center;
      flex: 1;

      .person-msg {
        margin-left: 3.07692vw;
        flex: 1;
        display: flex;
        flex-direction: column;

        .person-name {
          display: flex;
          align-items: center;
          font-size: 3.84615vw;
          color: #000000;

          .name {
            max-width: 41.02564vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 24PX;
          }
        }

        .person-desc {
          display: inline-block;
          font-size: 2.5641vw;
          color: #65676a;
          padding: 1.28205vw 3.07692vw;
          background: #F0F2F5;
          border-radius: 1.02564vw;
          line-height: 1;
          margin-top: 1.02564vw;
          width: max-content;
        }
      }
    }
  }

  .balance-wrap {
    display: flex;
    padding: 0 3.07692vw;
    margin-bottom: 5.12821vw;
    gap: 2.05128vw;

    .balance-item {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 3.84615vw 2.56410vw;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      border-radius: 2.05128vw;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        background: #f8f9fa;
        border-color: #0866ff;
      }

      .balance-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 8.71795vw;
        height: 8.71795vw;
        background: linear-gradient(135deg, rgb(78, 124, 220), rgb(60, 90, 166));
        border-radius: 2.05128vw;
        margin-right: 2.05128vw;
        flex-shrink: 0;

        .van-icon {
          font-size: 4.10256vw;
          color: #ffffff;
        }

        img {
          width: 4.10256vw;
          height: 4.10256vw;
          object-fit: contain;
        }
      }

      .balance-content {
        flex: 1;
        min-width: 0;
        overflow: hidden;

        .balance-label {
          font-size: 3.07692vw;
          color: #65676a;
          margin-bottom: 1.02564vw;
        }

        .balance-amount {
          font-size: 3.84615vw;
          font-weight: 600;
          color: #0866ff;
          word-break: break-all;
          line-height: 1.2;
        }
      }

      .arrow-icon {
        font-size: 3.58974vw;
        color: #65676a;
        margin-left: 1.53846vw;
        flex-shrink: 0;
      }
    }
  }

  .action-wrap {
    display: flex;
    padding: 0 3.07692vw;
    margin-bottom: 5.12821vw;
    gap: 2.05128vw;

    .action-item {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 3.84615vw 2.56410vw;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      border-radius: 2.05128vw;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        background: #f8f9fa;
        border-color: #0866ff;
      }

      .action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 8.71795vw;
        height: 8.71795vw;
        background: linear-gradient(135deg, rgb(78, 124, 220), rgb(60, 90, 166));
        border-radius: 2.05128vw;
        margin-right: 2.05128vw;
        flex-shrink: 0;

        .van-icon {
          font-size: 4.10256vw;
          color: #ffffff;
        }

        .svg-icon {
          font-size: 4.10256vw;
          color: #ffffff;
        }

        img {
          width: 4.10256vw;
          height: 4.10256vw;
          object-fit: contain;
          filter: brightness(0) invert(1);
        }
      }

      .action-content {
        flex: 1;
        min-width: 0;
        overflow: hidden;

        .action-label {
          font-size: 3.07692vw;
          color: #65676a;
          margin-bottom: 1.02564vw;
        }

        .action-value {
          font-size: 3.84615vw;
          font-weight: 600;
          color: #0866ff;
          word-break: break-all;
          line-height: 1.2;
        }
      }

      .arrow-icon {
        font-size: 3.58974vw;
        color: #65676a;
        margin-left: 1.53846vw;
        flex-shrink: 0;
      }
    }
  }

  .banner-wrap {
    padding: 0 4.10256vw;

    .banner {
      padding: 0 0 0 30.76923vw;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAswAAACgCAYAAAAYeDoSAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAVESURBVHgB7d0xchNXHMfxt092ihDP+AhykSIV+ARAm0o5AVcgVWbSIIoU6cwN4AZmcgCcE0CqzKRBN4gHxxTI2s2uggVB8s+UjPX5zGh2tLtS/Z3/vLfblM80mb7eX7ybT+podLdr2zulacb96f0CAABfvtPSdbOuNK+aUk7q7uj58fTg9HN+2Fx3Qx/K44t384e11gdFIAMAcEN0XXk62h097sN5lu67MpiHiXK7aB/1//SwAADADdV13dFvv3z741XXNwbzMFVu54sX/dVxAQCAm64rs7o7ur9p2lw/PTH5+a87YhkAgK3St+/QwN/3Lbx+6SMmywAAbLUNk+ZVMC/XLM8XL8UyAABbrY/mN+dnhydHh8unaKyWZCw3+IllAAC2Xd/Ee7e+efTha3m/FONi8boAAABLdWd0MCzNWE6YL+bzaQEAAFaGd5EMx2a5dvli8XcBAAA+dvrmn7ODOrzuugAAAJ/a39vbm9SuKfcKAACwppbmbq1NvV0AAIA1XdveGTb9jQsAALCuacZDMO8XAABgk/1aAACAKwlmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAhmAAAIBDMAAASCGQAAAsEMAACBYAYAgEAwAwBAIJgBACAQzAAAEAzBfFoAAIBNTodgnhUAAGBd181q6do/CgAAsKYpzavadeWkAAAAa7pSTuroq93jYh0zAACseXN+9rweTw+GWH5WAACAlaYrT0+ODpeb/kqdz48KAACw0lzMHw/HZTAf//rdrD88KQAAQGnb9sn7Rv7w4pK6M5qWziPmAADYcn0T77w9n15+XQXzsJa5Xszvi2YAALZW38JDEx8fHa4eivG/V2MPY+fatT+IZgAAts4Qy30LXy7FuNRsunfy05/jdmf3RX91XAAA4Ka7nCx/EsuDuun+5aR5d3RYbAQEAOCGGzb41fOzw02xPGiu+4P/ps2jaWnqgwIAADfDaV/Kz+picXRVKF+6NpgvTR6+3F98fWvSNOVeH8+3+1Pj/rNfAADgyzds4ps1TfOqXSx+H709P/54Y1/yL6m/9TlDbFA9AAAAAElFTkSuQmCC) no-repeat center center;
      background-size: contain;
      background: #4e7cdc;
      border-radius: 4.10256vw;
      min-height: 20.51282vw;

      img {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 20.51282vw;
        z-index: 0;
      }

      .title-wrap {
        position: relative;
        z-index: 1;
        color: #ffffff;
        margin-left: -6.41026vw;
        line-height: 1;

        .title {
          font-size: 6.15385vw;
          font-weight: 900;
          margin-bottom: 3.07692vw;
        }

        .desc {
          font-size: 4.10256vw;
        }
      }
    }
  }

  .tab-wrap {
    position: relative;
    padding: 0 4.10256vw;
    margin-top: 2.5641vw;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 1px;
      background: #e5e5e5;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }

  .section {
    margin-top: 5.64103vw;
    padding: 0 4.10256vw 4.61538vw;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6.15385vw;
      font-size: 4.10256vw;

      .title {
        font-size: 4.61538vw;
        font-weight: 500;
        color: #000000;
      }

      .desc {
        display: flex;
        align-items: center;
        font-size: 3.07692vw;
        color: #666;

        .svg-icon {
          color: #0866ff;
          font-size: 3.33333vw;
          margin-left: 1.02564vw;
          cursor: pointer;
        }
      }
    }

    .data-section {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 2.05128vw;

      .data-item {
        padding: 3.84615vw 3.58974vw 3.84615vw 4.61538vw;
        background: #ffffff;
        border-radius: 2.05128vw;
        border: 1px solid #C9CCD1;

        .number-line {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .progress-wrap {
            flex: 1;
            margin-right: 7.69231vw;
            margin-bottom: 1.28205vw;
          }

          .number {
            font-size: 4.61538vw;
            font-weight: 700;
            color: #000000;
          }
        }

        .data-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 3.07692vw;
          color: #000000;

          .svg-icon {
            font-size: 6.15385vw;
          }
        }
      }
    }

    .question-list {
      .question-item {
        margin-bottom: 9.23077vw;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .question {
          flex: 1;
          font-size: 4.10256vw;
          color: #65676a;
        }

        .van-icon {
          flex-shrink: 0;
          margin-left: 3.58974vw;
          font-size: 3.58974vw;
          color: #65676a;
        }
      }
    }
  }

  .tab-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4.10256vw 0;
    background: #ffffff;
    border-bottom: 1px solid #E5E5E5;
    border-top: 2.05128vw solid #F0F2F5;

    .tab-bar-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 3.33333vw;
      color: var(--van-black);
      padding: 0 1.02564vw;

      .svg-icon {
        font-size: 6.15385vw;
        margin-bottom: 2.05128vw;
      }

      .tab-text {
        font-size: 3.07692vw;
        text-align: center;
      }
    }
  }

  .vip-tag {
    margin-left: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    box-shadow: rgba(78, 124, 220, 0.25) 0px 2px 6px;
    position: relative;
    gap: 1.2vw;
    padding: 1.2vw 2.4vw;
    background: linear-gradient(135deg, rgb(78, 124, 220), rgb(60, 90, 166));
    border-radius: 3vw;
    transition: 0.3s;
    overflow: hidden;

    .vip-text {
      color: rgb(255, 255, 255);
      font-size: 3.2vw;
      font-weight: 600;
      text-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px;
      white-space: nowrap;
    }

    .vip-icon {
      font-size: 3.8vw;
      color: rgb(255, 255, 255);
      filter: drop-shadow(rgba(0, 0, 0, 0.1) 0px 1px 2px);
    }

    .arrow-icon {
      font-size: 2vw;
      color: rgba(255, 255, 255, 0.8);
      margin-left: 0.5vw;
    }
  }


  .dialog-wrap {
    padding: 10.25641vw 4.10256vw 4.10256vw;
    background: #ffffff;

    .dialog-title {
      text-align: center;
      font-weight: 700;
      font-size: 6.41026vw;
      margin-bottom: 12.82051vw;
    }

    .banner {
      width: 100%;
      height: auto;
      margin-bottom: 5.12821vw;
    }

    .text {
      font-size: 4.10256vw;
      color: #666;
      text-align: center;
      margin: 2.56410vw 0;
      line-height: 1.5;
    }

    .form-wrap {
      .input-wrap {
        display: flex;
        align-items: center;
        height: 16.92308vw;
        border-bottom: 1px solid #E5E5E5;

        /deep/ .van-cell {
          flex: 1;
          padding: 0;
          font-size: 5.64103vw;
          color: #000000;
          font-weight: 700;

          .van-field__label {
            width: auto;
            color: #000000;
          }
        }
      }

      .form-tips {
        display: flex;
        align-items: center;
        margin-top: 3.84615vw;
        font-size: 3.07692vw;

        .count-money {
          display: flex;
          align-items: center;
          margin-right: 2.5641vw;
          color: #000000;
        }

        .all-text {
          color: #0866ff;
          cursor: pointer;
        }
      }

      .coupon-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #F0F2F5;
        border-radius: 3.07692vw;
        height: 12.82051vw;
        padding: 0 4.61538vw;
        color: #000000;
        font-size: 3.07692vw;
        margin-top: 4.61538vw;

        .status {
          display: flex;
          align-items: center;

          .status-text {
            margin-right: 1.53846vw;
            font-size: 3.07692vw;
            font-weight: 500;
            color: #adadad;
          }

          .active {
            color: #0866ff;
          }
        }
      }
    }

    /deep/ .van-button {
      margin-top: 4.61538vw;
      border-radius: 16px;
    }
  }
}
</style>
