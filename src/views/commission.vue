<template>
  <div class="container">
    <van-nav-bar
      ref="navBar"
      :title="$t('佣金中心')"
      :border="false"
      fixed
      placeholder
      left-arrow
      @click-left="onClickLeft"
      z-index="9"
    >
      <template #right>
        <van-icon name="bell" @click="onClickNotification" />
      </template>
    </van-nav-bar>

    <!-- 佣金卡片区域 -->
    <div class="commission-cards">
      <div class="cards-grid">
        <div class="card-item">
          <div class="card-title">{{$t('总佣金')}}</div>
          <div class="card-amount">${{overviewData.commission.total}}</div>
        </div>
        <div class="card-item">
          <div class="card-title">{{$t('今日佣金')}}</div>
          <div class="card-amount">${{overviewData.commission.today}}</div>
        </div>
        <div class="card-item">
          <div class="card-title">{{$t('本周佣金')}}</div>
          <div class="card-amount">${{overviewData.commission.week}}</div>
        </div>
        <div class="card-item">
          <div class="card-title">{{$t('本月佣金')}}</div>
          <div class="card-amount">${{overviewData.commission.month}}</div>
        </div>
      </div>
    </div>

    <!-- 团队统计区域 -->
    <div class="team-stats">
      <div class="stats-item">
        <div class="stats-label">{{$t('团队总人数')}}</div>
        <div class="stats-value">{{overviewData.team.total}}</div>
      </div>
      <div class="stats-item">
        <div class="stats-label">{{$t('直属成员')}}</div>
        <div class="stats-value">{{overviewData.team.direct}}</div>
      </div>
      <div class="stats-item">
        <div class="stats-label">{{$t('间接成员')}}</div>
        <div class="stats-value">{{overviewData.team.indirect}}</div>
      </div>
    </div>

    <!-- 标签页区域 -->
    <div class="tabs-section">
      <van-tabs v-model="activeTab" sticky @change="onTabChange">
        <van-tab :title="$t('团体成员')">
          <div class="tab-content">
            <div class="filter-section">
              <van-dropdown-menu active-color="#1989fa">
                <van-dropdown-item v-model="teamFilterValue" :options="filterOptions" @change="onTeamFilterChange" />
              </van-dropdown-menu>
            </div>

            <!-- 团队成员列表 -->
            <van-list
              v-model="teamLoading"
              :finished="teamFinished"
              :loading-text="$t('加载中...')"
              :finished-text="teamMembers.length === 0 ? $t('暂无数据') : $t('没有更多了')"
              @load="loadMoreTeamMembers"
            >

              <div class="member-list" v-if="teamMembers.length > 0">
                <div class="member-item" v-for="member in teamMembers" :key="member.user_id">
                  <div class="member-info">
                    <img :src="member.avatar" alt="avatar" class="member-avatar" @error="handleImageError" />
                    <div class="member-details">
                      <div class="member-name">
                        {{member.nick_name}}
                        <span class="vip-badge" v-if="member.vip_level > 0">VIP{{member.vip_level}}</span>
                        <span class="level-badge">{{getLevelText(member.level_depth)}}</span>
                      </div>
                      <div class="member-register">
                        <van-icon name="clock-o" />
                        {{$t('注册时间')}} {{formatTime(member.register_time)}}
                      </div>
                    </div>
                  </div>
                  <div class="member-benefit">
                    <div class="benefit-amount">+${{member.benefit}}</div>
                    <div class="team-info">
                      <van-icon name="friends-o" />
                      {{$t('团队')}}: {{member.team_count}}{{$t('人')}}
                    </div>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </van-tab>
        <van-tab :title="$t('佣金明细')">
          <div class="tab-content">
            <div class="filter-section">
              <van-dropdown-menu active-color="#1989fa">
                <van-dropdown-item v-model="detailFilterValue" :options="filterOptions" @change="onDetailFilterChange" />
                <van-dropdown-item :title="dateText" :options="[]" @open="showDatePicker = true" />
              </van-dropdown-menu>
            </div>

            <!-- 佣金明细列表 -->
            <van-list
              v-model="detailLoading"
              :finished="detailFinished"
              :loading-text="$t('加载中...')"
              :finished-text="commissionDetails.length === 0 ? $t('暂无数据') : $t('没有更多了')"
              @load="loadMoreCommissionDetails"
            >

              <div class="detail-list" v-if="commissionDetails.length > 0">
                <div class="detail-item" v-for="detail in commissionDetails" :key="detail.id">
                  <div class="detail-info">
                    <div class="detail-name">{{detail.nickName}}</div>
                    <div class="detail-level">{{getLevelText(detail.levelDepth)}}</div>
                    <div class="detail-time">
                      <van-icon name="clock-o" />
                      {{$t('创建时间')}} {{formatTime(detail.createAt)}}
                    </div>
                  </div>
                  <div class="detail-money">
                    <div class="commission-amount">+${{detail.commissionAmount}}</div>
                    <div class="order-amount">{{$t('订单金额')}}: ${{detail.amount}}</div>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 日期选择弹窗 -->
    <van-calendar
      v-if="minDate && maxDate && defaultDate"
      v-model="showDatePicker"
      type="range"
      color="#1989fa"
      :title="$t('日期选择')"
      :confirm-text="$t('确认')"
      :show-title="true"
      :show-subtitle="false"
      :min-date="minDate"
      :max-date="maxDate"
      :default-date="defaultDate"
      @confirm="onDateConfirm"
      @close="closeDatePicker"
    />

  </div>
</template>

<script>
import { getCommissionOverview, getTeamMembers, getCommissionDetails } from '@/request/api'

// 常量定义
const LEVEL_ALL = 0
const LEVEL_DIRECT = 1
const LEVEL_INDIRECT = 2
const DEFAULT_PAGE_SIZE = 10
const SUCCESS_CODE = 200

export default {
  data() {
    return {
      // 页面状态
      activeTab: 0,
      teamFilterValue: 0,
      detailFilterValue: 0,
      showDatePicker: false,
      selectedDate: [],
      dateText: this.$t('请选择时间'),
      minDate: null,
      maxDate: null,
      defaultDate: null,

      // 数据存储
      overviewData: {
        commission: {
          total: '0.00',
          today: '0.00',
          week: '0.00',
          month: '0.00'
        },
        team: {
          total: 0,
          direct: 0,
          indirect: 0
        }
      },
      teamMembers: [],
      commissionDetails: [],

      // 加载状态
      teamLoading: false,
      detailLoading: false,
      teamFinished: false,
      detailFinished: false,

      // 分页信息
      teamPage: 1,
      detailPage: 1,
      pageSize: DEFAULT_PAGE_SIZE
    }
  },
  computed: {
    filterOptions() {
      return [
        { text: this.$t('全部'), value: LEVEL_ALL },
        { text: this.$t('直属下级'), value: LEVEL_DIRECT },
        { text: this.$t('间接下级'), value: LEVEL_INDIRECT }
      ]
    }
  },
  async mounted() {
    try {
      this.dateText = this.$t('请选择时间');
      this.initDateRange();

      // 加载概览数据
      this.loadOverviewData();

      // 加载默认的团队成员数据
      this.loadTeamMembers();

      // 确保导航栏占位符正确计算
      this.$nextTick(() => {
        setTimeout(() => {
          // 强制重新计算布局，解决首次加载时偶发的空白问题
          if (this.$refs.navBar) {
            this.$refs.navBar.$forceUpdate();
          }
          // 触发浏览器重排，确保布局正确
          document.body.offsetHeight;
        }, 100);
      });
    } catch (error) {
      console.error('mounted: 组件初始化失败', error);
    }
  },
  methods: {
    onClickLeft() {
      // 左侧返回按钮点击事件
      this.$router.go(-1);
    },

    onClickNotification() {
      // 右侧通知图标点击事件
      console.log('Notification clicked');
    },

    // 加载概览数据
    loadOverviewData() {
      getCommissionOverview().then((response) => {
        if (response && response.code === SUCCESS_CODE) {
          this.overviewData = response.data;
        } else {
          console.warn('概览数据返回异常:', response);
        }
      }).catch((error) => {
        console.error('加载概览数据失败:', error);
      });
    },

    // 加载团队成员数据
    loadTeamMembers(reset = true) {
      if (reset) {
        this.teamPage = 1;
        this.teamMembers = [];
        this.teamFinished = false;
      }

      this.teamLoading = true;

      const params = {
        level: this.teamFilterValue,
        pageNum: this.teamPage,
        pageSize: this.pageSize
      };

      getTeamMembers(params).then((response) => {
        this.teamLoading = false;

        if (response && response.code === SUCCESS_CODE) {
          const newItems = response.rows || [];
          const total = response.total || 0;

          if (reset) {
            this.teamMembers = newItems;
          } else {
            this.teamMembers = this.teamMembers.concat(newItems);
          }

          // 根据当前已加载数据总数与服务器返回的total比较判断是否已加载完所有数据
          if (this.teamMembers.length >= total || newItems.length === 0) {
            this.teamFinished = true;
          }
        } else {
          this.teamFinished = true; // 避免继续请求
        }
      }).catch((error) => {
        this.teamLoading = false;
        this.teamFinished = true; // 避免继续请求
        // this.$toast(this.$t('加载数据失败'));
      });
    },

    // 加载更多团队成员
    loadMoreTeamMembers() {
      if (this.teamFinished || this.teamLoading) return;
      this.teamPage++;
      this.loadTeamMembers(false);
    },

    // 加载佣金明细数据
    loadCommissionDetails(reset = true) {
      if (reset) {
        this.detailPage = 1;
        this.commissionDetails = [];
        this.detailFinished = false;
      }

      this.detailLoading = true;

      const params = {
        level: this.detailFilterValue,
        pageNum: this.detailPage,
        pageSize: this.pageSize,
        startTime: null,
        endTime: null
      };

      // 如果选择了日期范围，添加时间参数
      if (this.selectedDate && Array.isArray(this.selectedDate) && this.selectedDate.length === 2) {
        try {
          const startDate = this.selectedDate[0];
          const endDate = this.selectedDate[1];

          if (startDate instanceof Date && !isNaN(startDate.getTime()) &&
              endDate instanceof Date && !isNaN(endDate.getTime())) {
            params.startTime = Math.floor(startDate.getTime() / 1000);
            params.endTime = Math.floor(endDate.getTime() / 1000);
          }
        } catch (error) {
          console.error('日期范围处理失败:', error);
        }
      }

      getCommissionDetails(params).then((response) => {
        this.detailLoading = false;

        if (response && response.code === SUCCESS_CODE) {
          const newItems = response.rows || [];
          const total = response.total || 0;

          if (reset) {
            this.commissionDetails = newItems;
          } else {
            this.commissionDetails = this.commissionDetails.concat(newItems);
          }

          // 根据当前已加载数据总数与服务器返回的total比较判断是否已加载完所有数据
          if (this.commissionDetails.length >= total || newItems.length === 0) {
            this.detailFinished = true;
          }
        } else {
          this.detailFinished = true; // 避免继续请求
        }
      }).catch((error) => {
        this.detailLoading = false;
        this.detailFinished = true; // 避免继续请求
        // this.$toast(this.$t('加载数据失败'));
        console.error("加载数据失败")
      });
    },

    // 加载更多佣金明细
    loadMoreCommissionDetails() {
      if (this.detailFinished || this.detailLoading) return;
      this.detailPage++;
      this.loadCommissionDetails(false);
    },

    // 标签页切换
    onTabChange(index) {
      this.activeTab = index;
      if (index === 1 && this.commissionDetails.length === 0) {
        // 切换到佣金明细页且数据为空时加载数据
        this.loadCommissionDetails();
      }
    },

    // 团队成员筛选改变
    onTeamFilterChange() {
      this.teamPage = 1;
      this.teamLoading = false;
      this.teamFinished = false;
      this.teamMembers = [];
      this.loadTeamMembers(true);
    },

    // 佣金明细筛选改变
    onDetailFilterChange() {
      this.detailPage = 1;
      this.detailLoading = false;
      this.detailFinished = false;
      this.commissionDetails = [];
      this.loadCommissionDetails(true);
    },

         // 获取级别文本
     getLevelText(levelDepth) {
       switch (levelDepth) {
         case LEVEL_DIRECT:
           return this.$t('直属下级');
         case LEVEL_INDIRECT:
           return this.$t('间接下级');
         default:
           return this.$t('直属下级');
       }
     },

    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp || typeof timestamp !== 'number') {
        console.error('formatTime: 无效的时间戳', timestamp);
        return '--/--/--';
      }

      try {
        const date = new Date(timestamp * 1000);

        if (isNaN(date.getTime())) {
          console.error('formatTime: 无效的日期', timestamp);
          return '--/--/--';
        }

        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (error) {
        console.error('formatTime: 格式化时间失败', error, timestamp);
        return '--/--/--';
      }
    },

    initDateRange() {
      try {
        const now = new Date();

        // 验证日期对象是否有效
        if (isNaN(now.getTime())) {
          console.error('当前日期无效');
          this.setFallbackDates();
          return;
        }

        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth();

        // 设置最大日期为当前日期
        this.maxDate = new Date(now);

        // 设置最小日期为一年前的同月1号
        this.minDate = new Date(currentYear - 1, currentMonth, 1);

        // 设置默认日期为昨天和今天（range类型需要两个日期）
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        const today = new Date(now);
        this.defaultDate = [yesterday, today];

        // 设置初始显示文本
        const startDateText = this.formatDate(yesterday);
        const endDateText = this.formatDate(today);
        this.dateText = `${startDateText} - ${endDateText}`;
      } catch (error) {
        console.error('初始化日期范围失败:', error);
        this.setFallbackDates();
      }
    },

    // 设置回退日期值，确保日历组件不会收到null值
    setFallbackDates() {
      try {
        const now = new Date();
        const fallbackDate = new Date(2024, 0, 1); // 2024年1月1日作为回退日期

        // 如果当前日期有效，使用当前日期，否则使用回退日期
        const validDate = !isNaN(now.getTime()) ? now : fallbackDate;

        this.maxDate = new Date(validDate);
        this.minDate = new Date(validDate.getFullYear() - 1, validDate.getMonth(), 1);

        const yesterday = new Date(validDate);
        yesterday.setDate(validDate.getDate() - 1);
        const today = new Date(validDate);
        this.defaultDate = [yesterday, today];

        this.dateText = this.$t('请选择时间');
      } catch (fallbackError) {
        console.error('设置回退日期失败:', fallbackError);
        // 最后的回退方案 - 使用固定的日期
        this.maxDate = new Date(2024, 11, 31); // 2024年12月31日
        this.minDate = new Date(2023, 0, 1);   // 2023年1月1日
        this.defaultDate = [new Date(2024, 11, 30), new Date(2024, 11, 31)];
        this.dateText = this.$t('请选择时间');
      }
    },

    closeDatePicker() {
      this.showDatePicker = false;
    },

    onDateConfirm(value) {
      if (value && Array.isArray(value) && value.length === 2) {
        try {
          const startDate = this.formatDate(value[0]);
          const endDate = this.formatDate(value[1]);
          this.dateText = `${startDate} - ${endDate}`;
          this.selectedDate = value;

          // 日期确认后重新加载佣金明细数据
          this.loadCommissionDetails(true);
        } catch (error) {
          console.error('格式化日期失败:', error);
          this.dateText = this.$t('请选择时间');
        }
      }
      this.showDatePicker = false;
    },

    formatDate(date) {
      if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        console.error('formatDate: 无效的日期对象', date);
        return '00/00';
      }

      try {
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${month}/${day}`;
      } catch (error) {
        console.error('formatDate: 格式化日期失败', error, date);
        return '00/00';
      }
    },

    // 处理图片加载错误
    handleImageError(event) {
      // 设置默认头像
      event.target.src = require('@/assets/avatar.png');
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 佣金卡片样式 */
.commission-cards {
  padding: 20px ;
  margin-left: 16px;
  margin-right: 16px;
  border-radius: 12px 12px 0 0 ;
  background: linear-gradient(135deg, #4f46e5, #2563eb);
  margin-top: 18px;
}

.cards-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.card-item {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 16px 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 6px;
  line-height: 1.2;
  text-align: center;
  word-break: break-word;
  hyphens: auto;
}

.card-amount {
  color: white;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  line-height: 1.1;
}

/* 团队统计样式 */
.team-stats {
  display: flex;
  background: white;
  padding: 20px 0;
  margin: 0 16px;
  border-radius: 0 0 12px 12px;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #ebedf0;
}

.stats-label {
  color: #969799;
  font-size: 11px;
  margin-bottom: 6px;
  line-height: 1.2;
  word-break: break-word;
  hyphens: auto;
}

.stats-value {
  color: #1989fa;
  font-size: 24px;
  font-weight: bold;
}

/* 标签页样式 */
.tabs-section {
  margin-top: 20px;
  background: white;
}

.tab-content {
  min-height: 300px;
}

.filter-section {
  padding: 0 16px;
  background: white;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-text {
  color: #969799;
  font-size: 14px;
}

/* 团队成员列表样式 */
.member-list {
  padding: 0 16px;
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #ebedf0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.vip-badge {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: bold;
}

.level-badge {
  background: #1989fa;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

.member-register {
  font-size: 12px;
  color: #969799;
  display: flex;
  align-items: center;
  gap: 4px;
}

.member-benefit {
  text-align: right;
}

.benefit-amount {
  font-size: 18px;
  font-weight: bold;
  color: #ff4757;
  margin-bottom: 4px;
}

.team-info {
  font-size: 12px;
  color: #969799;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 佣金明细列表样式 */
.detail-list {
  padding: 0 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #ebedf0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-info {
  flex: 1;
}

.detail-name {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

.detail-level {
  background: #1989fa;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  display: inline-block;
  margin-bottom: 8px;
}

.detail-time {
  font-size: 12px;
  color: #969799;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-money {
  text-align: right;
}

.commission-amount {
  font-size: 18px;
  font-weight: bold;
  color: #ff4757;
  margin-bottom: 4px;
}

.order-amount {
  font-size: 12px;
  color: #969799;
}

/* 自定义确定按钮样式 */
:deep(.date-picker-footer .van-button) {
  background-color: #0866ff !important;
  border-color: #0866ff !important;
  border-radius: 4px !important;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

:deep(.date-picker-footer ) {
  background-color: #0653d3 !important;
  border-color: #0653d3 !important;
}

/* 确保日历组件正常显示 */
:deep(.calendar-container ) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  background: white !important;
}

/* 覆盖vant样式 - 确保导航栏在最上层 */
:deep(.van-nav-bar) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 999 !important;
  height: 44px !important; /* 明确设置导航栏高度，避免计算问题 */
}

:deep(.van-nav-bar .van-nav-bar__title) {
  color: #323233;
}

:deep(.van-nav-bar .van-icon) {
  color: #323233;
}

:deep(.van-tabs__nav) {
  background: white;
}

:deep(.van-tab) {
  color: #969799;
}

:deep(.van-tab--active) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}
</style>
