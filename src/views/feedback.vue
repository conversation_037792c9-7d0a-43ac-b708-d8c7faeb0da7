<template>
    <div class="container">
        <van-nav-bar :title="$t('意见反馈')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />
        <div class="container_in">
            <div class="section">
                <div class="page-title">{{ $t('请输入您想反馈的问题！') }}</div>
                <div class="form-item">
                    <van-field v-model="title" :placeholder="$t('请输入您的标题')" />
                </div>
                <div class="form-item">
                    <van-field v-model="content" type="textarea" :placeholder="$t('您宝贵的意见，就是我们进步的源泉')" rows="8" />
                </div>
            </div>
        </div>
        <div class="btn-wrap">
            <van-button type="primary" color="#0866ff" size="large" @click="getSubmit">{{ $t('提交') }}</van-button>
        </div>
    </div>
</template>
<script>
import { feedback } from '@/request/api'
export default {
    data() {
        return {
            title: "",
            content: ""
        };
    },
    mounted() {

    },
    methods: {
        getSubmit() {
            if (!this.title) {
                this.$toast(this.$t('请输入您的标题'))
                return
            }
            if (!this.content) {
                this.$toast(this.$t('请输入您的意见内容'))
                return
            }
            const params = {
                title: this.title,
                content: this.content,
            };
            feedback(params).then((res) => {
                if (res.code == 200) {
                    this.$toast.success(this.$t('提交成功'))
                }
            });
        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    min-height: 100vh;
    position: relative;

    .container_in {
        padding: 5.12821vw 4.10256vw;


        .section {
            flex: 1;
            margin-bottom: 5.12821vw;

            .page-title {
                font-size: 4.61538vw;
                font-weight: 500;
            }

            .form-item {
                margin-top: 5.12821vw;

                /deep/ .van-field {
                    background-color: #f0f2f5;
                    font-size: 3.33333vw;
                    border-radius: 2.05128vw;
                }
            }
        }
    }

    .btn-wrap {
        position: absolute;
        bottom: 5.12821vw;
        width: -webkit-fill-available;
        padding: 0 4.10256vw;

        .van-button {
            width: 100%;
            border-radius: 16px;
        }
    }
}
</style>