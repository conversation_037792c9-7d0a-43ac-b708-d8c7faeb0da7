<template>
    <div class="container">
        <van-nav-bar :title="$t('产品详情')" fixed :border="false" placeholder left-arrow @click-left="onClickLeft" />
        <div class="header-wrap">
            <div class="logo"><img :src="detail.app ? detail.app.logo : ''">
            </div>
            <div class="title">{{ detail.app ? detail.app.name : '' }}</div>
            <div class="btn-wrap" v-if="false">
                <img src="../assets/google-play.png" @click="getLink(detail.app.googlePlay)">
                <img src="../assets/apple-store.png" @click="getLink(detail.app.appStore)">
            </div>
        </div>
        <div class="section" style="padding-right: 0px;" v-if="detail.app">
            <div class="banner-wrap">
                <van-swipe :loop="false" :width="350" :show-indicators="false">
                    <van-swipe-item v-for="(item, index) in detail.app.image.split(',')" :key="index">
                        <img :src="item" alt="">
                    </van-swipe-item>
                </van-swipe>
            </div>
        </div>
        <div class="section">
            <div class="section-title">{{$t('游戏简介')}}</div>
            <div class="put-desc">{{ detail.app ? detail.app.description : '' }}</div>
        </div>
        <div class="section">
            <div class="section-title">{{$t('产品详情')}}</div>
            <div class="form-cell-wrap" v-if="detail.app">
                <div class="cell-item" v-for="(item, index) in detail.app.productInfo" :key="index">
                    <div class="label">{{ item.title }}</div>
                    <div class="content">{{ item.value }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { PlanDetail } from '@/request/api'
export default {
    data() {
        return {
            id: "",
            detail: {}
        };
    },
    mounted() {
        this.id = this.$route.query.orderNo
        this.getDetail()
    },
    methods: {
        getLink(val){
            window.location.href = val
        },
        getDetail() {
            PlanDetail(this.id).then((res) => {
                if (res.code == 200) {
                    this.detail = res.data
                    this.detail.app.productInfo = JSON.parse(this.detail.app.productInfo)
                    console.log(this.detail)
                }
            });
        },
        onClickLeft() {
            this.$router.go(-1)
        }
    },
};
</script>
<style lang="less" scoped>
.container {
    padding-bottom: 30px;

    .header-wrap {
        display: flex;
        align-items: center;
        flex-direction: column;
        background: url(../assets/goods-detail-header-bg.png) no-repeat;
        padding: 6.66667vw 6.41026vw;
        padding-top: calc(constant(safe-area-inset-top) + 20vw);
        padding-top: calc(env(safe-area-inset-top) + 10vw);

        .logo {
            width: 24.61538vw;
            height: 24.61538vw;

            img {
                width: 100%;
                height: 100%;
                border-radius: 4.61538vw;
            }
        }

        .title {
            font-size: 5.89744vw;
            font-weight: 600;
            margin-top: 6.66667vw;
        }

        .btn-wrap {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2.82051vw;
            margin-top: 7.17949vw;

            img {
                height: 12.30769vw;
            }
        }
    }

    .section {
        margin-top: 6.15385vw;
        padding: 0 3.84615vw;

        .banner-wrap {
            /deep/ .van-swipe-item {
                padding: 0 .76923vw;

                img {
                    display: block;
                    flex-shrink: 0;
                    width: 89.74359vw;
                    height: 41.53846vw;
                    border-radius: 4.10256vw;
                }
            }
        }

        .section-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 4.10256vw;
            font-size: 4.10256vw;
        }

        .put-desc {
            font-size: 3.07692vw;
            color: #000000;
        }

        .form-cell-wrap {
            padding: 3.58974vw 4.61538vw;
            border-radius: 3.07692vw;
            background: #F0F2F5;

            .cell-item {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                color: #000000;
                margin-bottom: 5.12821vw;

                &:last-child {
                    margin-bottom: 0;
                }

                .label {
                    flex-shrink: 0;
                    font-size: 3.07692vw;
                    width: 20.51282vw;
                }

                .content {
                    font-size: 3.07692vw;
                    flex: 1;
                    margin-left: 10.25641vw;
                    text-align: right;
                    word-break: break-all;
                }
            }
        }
    }
}
</style>
