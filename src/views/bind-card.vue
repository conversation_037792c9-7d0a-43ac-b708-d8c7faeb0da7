<template>
  <div class="container">
    <van-nav-bar :title="$t('设置提现方式')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft"/>

    <!-- 广告 Banner -->
    <div class="ad-banner">
      <div class="banner-content">
        <div class="left-section">
          <div class="title-section">
            <span class="title">{{ $t('店铺星级') }}</span>
            <img src="@/assets/sotre_start.png" alt="店铺星级" class="store-icon"/>
          </div>
        </div>
        <div class="right-section" v-if="false">
          <div class="star-button">{{ $t('一星') }}</div>
        </div>
      </div>
      <div class="banner-desc" v-if="false">
        {{ $t('1星用户每投放一个广告可获得投资额12%的佣金，每天需投放3个广告。') }}
      </div>
    </div>


    <div class="container_nei">
      <!-- 筛选组件 -->
      <div class="filter-section">
        <div class="filter-label">
          <label>{{ $t('提现方式') }}</label>
        </div>
        <div class="filter-field" @click="showFilterPopup = true">
          <div class="filter-value">
            <span>{{
              selectedMethod === 'bank card' ? $t('银行卡') :
              selectedMethod === 'Crypto' ? $t('加密货币') :
              $t('选择提现方式')
            }}</span>
          </div>
          <van-icon name="arrow" class="arrow-icon"/>
        </div>
      </div>

      <div class="form-wrap" v-if="isShowBankCard" id="bankCard">
        <div class="form-item">
          <div class="label">{{ $t('银行名称') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.bankName" :placeholder="$t('请输入银行名称')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('用户姓名') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.accountName" :placeholder="$t('请输入用户姓名')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('手机号') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.phoneNumber" :placeholder="$t('请输入手机号')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('银行账户') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.bankAccount" :placeholder="$t('请输入银行账户')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('Swift代码（非必填）') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.swiftCode" :placeholder="$t('请输入Swift代码')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('银行代码（非必填）') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.bankCode" :placeholder="$t('请输入银行代码')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('银行地址（非必填）') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.bankAddress" :placeholder="$t('请输入银行地址')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('收款人地址（非必填）') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.recipientAddress" :placeholder="$t('请输入收款人地址')"/>
          </div>
        </div>
        <div class="form-item">
          <div class="label">{{ $t('邮编（非必填）') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.postalCode" :placeholder="$t('请输入邮编')"/>
          </div>
        </div>
      </div>

      <div class="form-wrap" v-if="isShowCrypto" id="crypto">
        <div class="filter-section">
          <div class="filter-label">
            <label>{{ $t('货币类型') }}</label>
          </div>
          <div class="filter-field" @click="showCurrencyPopup = true">
            <div class="filter-value">
              <span>{{ currencyType || $t('选择提现方式') }}</span>
            </div>
            <van-icon name="arrow" class="arrow-icon"/>
          </div>
        </div>

        <div class="form-item" v-if="currencyType === 'BTC'">
          <div class="label">{{ $t('提币地址') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.btcAdr" :placeholder="$t('请输入提币地址')"/>
          </div>
        </div>

        <div class="form-item" v-if="currencyType === 'ETH'">
          <div class="label">{{ $t('提币地址') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.ethAdr" :placeholder="$t('请输入提币地址')"/>
          </div>
        </div>

        <div class="form-item" v-if="currencyType === 'USDT'">
          <div class="label">{{ $t('提币地址') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.usdtAdr" :placeholder="$t('请输入提币地址')"/>
          </div>
        </div>

        <div class="form-item" v-if="currencyType === 'USDC'">
          <div class="label">{{ $t('提币地址') }}</div>
          <div class="input-wrap">
            <van-field v-model="form.usdcAdr" :placeholder="$t('请输入提币地址')"/>
          </div>
        </div>

      </div>

      <div class="btn-wrap">
        <van-button type="primary" color="#0866ff" size="large" @click="getBind">{{ $t('确认绑定') }}</van-button>
      </div>
    </div>

    <van-popup v-model="show" position="bottom" @click-overlay="onClickLeft">
      <div class="dialog-wrap" :style="showKeyboard == true ? 'padding-bottom: 310px;' : ''">
        <div class="dialog-title">{{ $t('交易密码') }}</div>
        <div class="desc">{{ $t('请输入您的交易密码') }}</div>
        <van-password-input :value="password" :length="6" :focused="showKeyboard"
                            @focus="showKeyboard = true"/>
        <div class="btn-wrap">
          <van-button type="primary" color="#0866ff" size="large"
                      :disabled="password.length != 6 ? true : false" @click="getPassVerify">{{ $t('确认') }}
          </van-button>
        </div>
        <van-number-keyboard v-model="password" :maxlength="6" :show="showKeyboard"
                             @blur="showKeyboard = false"/>
      </div>
    </van-popup>

    <!-- 筛选弹窗 -->
    <van-popup v-model="showFilterPopup" position="bottom" round>
      <div class="filter-popup">
        <div class="popup-header">
          <span class="popup-title">{{ $t('提现方式') }}</span>
          <van-icon name="cross" @click="showFilterPopup = false" class="close-icon"/>
        </div>
        <div class="filter-options">
          <div
            class="filter-option"
            :class="{ active: selectedMethod === 'bank card' }"
            @click="selectMethod('bank card')"
          >
            <span>{{$t('银行卡')}}</span>
            <van-icon v-if="selectedMethod === 'bank card'" name="success" class="check-icon"/>
          </div>
          <div
            class="filter-option"
            :class="{ active: selectedMethod === 'Crypto' }"
            @click="selectMethod('Crypto')"
          >
            <span>{{ $t('加密货币') }}</span>
            <van-icon v-if="selectedMethod === 'Crypto'" name="success" class="check-icon"/>
          </div>
        </div>
        <div class="popup-buttons">
          <van-button class="cancel-btn" @click="showFilterPopup = false">{{ $t('取消') }}</van-button>
          <van-button type="primary" color="#1989fa" @click="confirmSelection">{{ $t('确认') }}</van-button>
        </div>
      </div>
    </van-popup>

    <!-- Currency type 弹窗 -->
    <van-popup v-model="showCurrencyPopup" position="bottom" round>
      <div class="filter-popup">
        <div class="popup-header">
          <span class="popup-title">{{ $t('货币类型') }}</span>
          <van-icon name="cross" @click="showCurrencyPopup = false" class="close-icon"/>
        </div>

        <div class="filter-options">
          <div
            class="filter-option"
            :class="{ active: currencyType === 'BTC' }"
            @click="selectCurrency('BTC')"
          >
            <span>BTC</span>
            <van-icon v-if="currencyType  === 'BTC'" name="success" class="check-icon"/>
          </div>
          <div
            class="filter-option"
            :class="{ active: currencyType  === 'ETH' }"
            @click="selectCurrency('ETH')"
          >
            <span>ETH</span>
            <van-icon v-if="currencyType  === 'ETH'" name="success" class="check-icon"/>
          </div>

          <div
            class="filter-option"
            :class="{ active: currencyType  === 'USDC' }"
            @click="selectCurrency('USDC')"
          >
            <span>USDC</span>
            <van-icon v-if="currencyType  === 'USDC'" name="success" class="check-icon"/>
          </div>
          <div
            class="filter-option"
            :class="{ active: currencyType  === 'USDT' }"
            @click="selectCurrency('USDT')"
          >
            <span>USDT</span>
            <van-icon v-if="currencyType  === 'USDT'" name="success" class="check-icon"/>
          </div>

        </div>
        <div class="popup-buttons">
          <van-button class="cancel-btn" @click="showCurrencyPopup = false">{{ $t('取消') }}</van-button>
          <van-button type="primary" color="#1989fa" @click="confirmCurrencySelection">{{ $t('确认') }}</van-button>
        </div>
      </div>
    </van-popup>


  </div>
</template>
<script>
import { getUserInfo, editInfo } from '@/request/api'

export default {
  data () {
    return {
      show: true,
      password: '',
      showKeyboard: true,
      isShowBankCard: false,
      isShowCrypto: false,
      value: '',
      form: {
        bankName: '',
        accountName: '',
        phoneNumber: '',
        bankAccount: '',
        swiftCode: '',
        bankCode: '',
        bankAddress: '',
        recipientAddress: '',
        postalCode: '',
        btcAdr: '',
        ethAdr: '',
        usdtAdr: '',
        usdcAdr: '',

      },
      showFilterPopup: false,
      selectedMethod: '',
      currencyType: '',
      showCurrencyPopup: false,
    }
  },
  mounted () {
    this.getUserDetail()
  },
  watch: {
    password (val) {
      if (val.length === 6) {
        this.showKeyboard = false
      }
    },
  },
  methods: {
    // 绑定银行卡
    getBind () {
      if (this.isShowBankCard) {
        if (!this.form.bankName) {
          this.$toast(this.$t('请输入银行名称'))
          return
        }
        if (!this.form.accountName) {
          this.$toast(this.$t('请输入用户姓名'))
          return
        }
        if (!this.form.phoneNumber) {
          this.$toast(this.$t('请输入手机号'))
          return
        }
        if (!this.form.bankAccount) {
          this.$toast(this.$t('请输入银行账户'))
          return
        }

        if (!this.form.swiftCode) {
          this.$toast(this.$t('请输入Swift代码'))
          return
        } if (!this.form.bankCode) {
          this.$toast(this.$t('请输入银行代码'))
          return
        }

      } else if (this.isShowCrypto) {

        if (!this.currencyType) {
          this.$toast(this.$t('选择提现方式'))
          return
        }

        let currentAddress = ''
        if (this.currencyType === 'BTC') {
          currentAddress = this.form.btcAdr
        } else if (this.currencyType === 'ETH') {
          currentAddress = this.form.ethAdr
        } else if (this.currencyType === 'USDT') {
          currentAddress = this.form.usdtAdr
        } else if (this.currencyType === 'USDC') {
          currentAddress = this.form.usdcAdr
        }

        if (!currentAddress) {
          this.$toast(this.$t('请输入地址'))
          return
        }

      } else {
        this.$toast(this.$t('请先选择绑定方式'))
        return
      }

      editInfo(this.form).then((res) => {
        if (res.code === 200) {
          this.$toast.success(this.$t('绑定成功'))
          setTimeout(() => {
            this.$router.go(-1)
          }, 1000)
        } else {
          this.$toast(res.msg)
        }
      })
    },
    // 用户信息
    getUserDetail () {
      const params = {}
      getUserInfo(params).then((res) => {
        if (res.code === 200) {
          this.userInfo = res.data
          this.form = this.userInfo
          // if (this.userInfo.btcAdr){
          //   this.currencyType = 'BTC'
          // }else  if (this.userInfo.ethAdr){
          //   this.currencyType = 'ETH'
          // }else  if (this.userInfo.usdtAdr){
          //   this.currencyType = 'USDT'
          // }else  if (this.userInfo.usdcAdr){
          //   this.currencyType ='USDC'
          // }
        }
      })
    },
    // 验证交易密码
    getPassVerify () {
      if (this.userInfo.password !== this.password) {
        this.$toast(this.$t('交易密码错误'))
        return
      }
      this.show = false
    },
    onClickLeft () {
      this.$router.go(-1)
    },
    selectMethod (method) {
      this.selectedMethod = method
    },
    confirmSelection () {
      this.showFilterPopup = false
      if (this.selectedMethod === 'bank card') {
        this.isShowBankCard = true
        this.isShowCrypto = false
      } else if (this.selectedMethod === 'Crypto') {
        this.isShowBankCard = false
        this.isShowCrypto = true

      } else {
        this.isShowBankCard = false
        this.isShowCrypto = false
      }
    },
    selectCurrency (currency) {
      this.currencyType = currency
    },
    confirmCurrencySelection () {
      this.showCurrencyPopup = false
    },

  },
}
</script>
<style lang="less" scoped>
.container {
  .ad-banner {
    background: linear-gradient(135deg, #4e7cdc, #6c92e3);
    margin: 4.10256vw;
    border-radius: 16px;
    padding: 4.10256vw;
    color: #ffffff;

    .banner-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2.05128vw;

      .left-section {
        display: flex;
        align-items: center;
        flex: 1;

        .store-icon {
          width: 4.61538vw;
          height: 4.61538vw;
          margin-left: 0.76923vw;
        }

        .title-section {
          display: flex;
          align-items: center;

          .title {
            font-size: 4.61538vw;
            font-weight: 600;
            margin-right: 1.53846vw;
          }

          .star-icon {
            font-size: 4.61538vw;
          }
        }
      }

      .right-section {
        .star-button {
          background: #fff3;
          color: #ffffff;
          padding: 1.02564vw 2.56410vw;
          border-radius: 8px;
          font-size: 3.58974vw;
          font-weight: 500;
          white-space: nowrap;
        }
      }
    }

    .banner-desc {
      font-size: 3.07692vw;
      line-height: 1.4;
      opacity: 0.9;
    }
  }

  .container_nei {
    display: flex;
    flex-direction: column;
    padding: 5.12821vw 4.10256vw;

    .filter-section {
      display: flex;
      align-items: center;
      margin-bottom: 8.20513vw;
      padding-bottom: 8.20513vw;
      border-bottom: 1px solid #E5E5E5;

      .filter-label {
        width: 25.64103vw;
        margin-right: 4.10256vw;

        label {
          font-size: 3.73333vw;
          color: #000000;
          line-height: 1.2;
          white-space: normal;
          word-break: break-word;
          text-align: left;
          display: block;
        }
      }

      .filter-field {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        cursor: pointer;

        .filter-value {
          flex: 1;

          span {
            font-size: 3.58974vw;
            color: #969799;
            line-height: 1;
          }
        }

        .arrow-icon {
          font-size: 4.61538vw;
          color: #C8C9CC;
          margin-left: 2.56410vw;
        }
      }
    }

    .form-wrap {
      flex: 1;

      .form-item {
        margin-bottom: 8.20513vw;

        .label {
          font-size: 4.61538vw;
          color: #000000;
          margin-bottom: 5.12821vw;
          line-height: 1;
        }

        .input-wrap {
          padding-bottom: 8.20513vw;
          border-bottom: 1px solid #E5E5E5;

          /deep/ .van-field {
            padding: 0;
            font-size: 3.58974vw;
            color: #000000;
            line-height: 1;
          }
        }
      }
    }

    .btn-wrap {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);

      .van-button {
        width: 100%;
        border-radius: 16px;
      }
    }
  }

  .dialog-wrap {
    padding: 10.25641vw 4.10256vw 4.10256vw;
    background: #ffffff;
    transition: padding .3s;


    .dialog-title {
      text-align: center;
      font-weight: 700;
      font-size: 6.41026vw;
      margin-bottom: 12.82051vw;
    }

    .desc {
      text-align: center;
      font-size: 3.84615vw;
      color: #666666;
      margin-bottom: 7.69231vw;
    }

    .btn-wrap {
      margin-top: 10.25641vw;

      .van-button {
        width: 100%;
        border-radius: 16px;
      }
    }
  }

  .filter-popup {
    padding: 6.41026vw 4.10256vw 4.10256vw;
    background: #ffffff;
    border-radius: 20px 20px 0 0;

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6.41026vw;

      .popup-title {
        font-size: 5.12821vw;
        font-weight: 600;
        color: #323233;
      }

      .close-icon {
        font-size: 5.12821vw;
        color: #969799;
      }
    }

    .filter-options {
      margin-bottom: 8.20513vw;

      .filter-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4.10256vw;
        background-color: #f7f8fa;
        border-radius: 12px;
        margin-bottom: 3.07692vw;

        span {
          font-size: 4.10256vw;
          color: #646566;
        }

        .check-icon {
          font-size: 4.61538vw;
          color: #1989fa;
        }

        &.active {
          background-color: #f7f8fa;

          span {
            color: #323233;
          }
        }
      }
    }

    .popup-buttons {
      display: flex;
      gap: 3.07692vw;

      .cancel-btn {
        flex: 1;
        background-color: #ffffff;
        color: #646566;
        border: 1px solid #ebedf0;
        border-radius: 6px;
        height: 11.53846vw;
        font-size: 4.10256vw;
      }

      .van-button--primary {
        flex: 1;
        border-radius: 6px;
        height: 11.53846vw;
        font-size: 4.10256vw;
      }
    }
  }
}
</style>
