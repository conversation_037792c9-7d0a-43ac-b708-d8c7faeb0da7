<template>
    <div class="container">
        <van-nav-bar :title="$t('在线客服')" :border="false" left-arrow placeholder fixed @click-left="onClickLeft" />
        <div class="container_in">
            <iframe :src="userInfo.customerServiceUrl" frameborder="0" width="100%" height="100%"></iframe>
        </div>
    </div>
</template>
<script>
import { getUserInfo } from '@/request/api'
export default {
    data() {
        return {
            userInfo:""
        };
    },
    mounted() {
        this.getUserDetail()
    },
    methods: {
        // 用户信息
        getUserDetail() {
            const params = {};
            getUserInfo(params).then((res) => {
                if (res.code == 200) {
                    this.userInfo = res.data
                }
            });
        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .container_in {
        height: calc(100vh - 13.33333vw);
    }
}
</style>