<!-- AdWelcome.vue -->
<template>
    <div class="ad-welcome">
        <!-- 顶部标题 -->
        <div class="header-title">{{$t('广告中心')}}</div>


        <!-- 插图区域 -->
        <div class="illustration-area">
            <img src="@/assets/ad-welcome-banner-Br0Bw8-x.png" alt="">
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <div class="welcome-section">
                <h2 class="welcome-text">{{$t('欢迎使用')}}</h2>
                <div class="facebook-section">
                    <div class="facebook-logo">
                        <img src="@/assets/Facebook_f_logo.png" alt="Facebook Logo">
                    </div>
                    <h2 class="network-text">{{$t('广告网络')}}</h2>
                </div>
            </div>

            <!-- 用户数据信息 -->
            <div class="stats-section">
                <p class="user-stats">{{$t('帮助更多产品触达超过30亿Facebook用户')}}</p>
                <!-- <p class="connection-text">快速建立连接</p> -->
            </div>

            <!-- 功能特点列表 -->
            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon message-icon">
                        <img src="@/assets/Facebook_f_logo.png" alt="">
                    </div>
                    <div class="feature-content">
                        <h3>{{$t('免费开户')}}</h3>
                        <p>{{$t('使用授权码开设广告账户，无需任何费用')}}</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon service-icon">
                        <img src="@/assets/Facebook_f_logo.png" alt="">
                    </div>
                    <div class="feature-content">
                        <h3>{{$t('产品运营服务')}}</h3>
                        <p>{{$t('产品操作流行，线上结算便捷')}}</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon ai-icon">
                        <img src="@/assets/Facebook_f_logo.png" alt="">
                    </div>
                    <div class="feature-content">
                        <h3>{{$t('智能广告投放')}}</h3>
                        <p>{{$t('大数据AI算法，精准受众定位')}}</p>
                    </div>
                </div>
            </div>

            <!-- 开设账户按钮 -->
            <button class="create-account-btn" @click="showModal">
                {{$t('开设账户')}}
            </button>
        </div>

        <!-- 弹出层 -->
        <div class="modal" v-if="isModalVisible">
            <div class="modal-overlay" @click="hideModal"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2>{{$t('开设账户')}}</h2>
                    <!-- <button class="close-btn" @click="hideModal">&times;</button> -->
                </div>
                <img src="@/assets/adcenter-open-tips-DtmdRheM.png"
                    style=" width: 100%;display: block;margin:  auto;" />
                <div class="modal-body">
                    <form>
                        <div class="form-group">
                            <label for="username">{{$t('请输入您的授权码')}}</label>
                            <input type="text" id="activationCode" v-model="formData.activationCode">
                        </div>

                        <div class="form-group">
                            <button @click="callParentMethod" class="submit-btn"
                                :class="formData.activationCode && formData.activationCode.length > 0 ? 'Color1664D9' : ''">{{$t('现在开始')}}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'AdWelcome',
    data() {
        return {
            isModalVisible: false,
            formData: {
                activationCode: ''
            }
        }
    },
    methods: {
        showModal() {
            this.isModalVisible = true
        },
        hideModal() {
            this.isModalVisible = false
        },
        handleSubmit() {
            console.log('Form submitted:', this.formData)
            this.hideModal()
        },
        callParentMethod() {
            if (this.formData.activationCode && this.formData.activationCode.length > 0) {
                let code = this.formData.activationCode;
                this.$emit('custom-event', code); // 触发事件
                this.formData.activationCode = '';
                this.hideModal();
            }

        }
    }
}
</script>

<style scoped>
.ad-welcome {
    min-height: 100%;
    background-color: white;
    color: black;
    /* padding: 40px 20px; */
    text-align: center;
    /* display: flex; */
    flex-direction: column;
    align-items: center;
}

.header-title {
    top: 10px;
    position: absolute;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
    width: 100%;
}

.heart-icon {
    color: #1877F2;
    font-size: 24px;
    margin: 10px 0;
}

.illustration-area {
    width: 100%;
    max-width: 600px;
    /* margin: 20px auto; */
}

.illustration-area img {
    width: 100%;
    height: auto;
}

.main-content {
    max-width: 500px;
    margin: 0 auto;
}

.welcome-section {
    margin-bottom: 30px;
}

.welcome-text {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 20px;
}

.facebook-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.facebook-logo {
    width: 50px;
    height: 50px;
}

.facebook-logo img {
    width: 100%;
    height: 100%;
}

.network-text {
    font-size: 6.41026vw;;
    font-weight: 600;
}

.stats-section {
    margin: 30px 0;
}

.user-stats {
    font-size: 3.33333vw;
    line-height: 1.5;
    /* margin-bottom: 10px; */
    color: #646566;
}

.connection-text {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

.features-list {
    margin: 40px 16px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 0.4rem;
    text-align: left;
}

.feature-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon img {
    width: 24px;
    height: 24px;
}

.feature-content h3 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 4px;
}

.feature-content p {
    font-size: 3.33333vw;
    color: #646566;
    line-height: 1.4;
}

.create-account-btn {
    width: 90%;
    padding: 15px 0;
    background-color: #0A59CC;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.create-account-btn:hover {
    background-color: #0947A3;
}

/* 弹出层样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
    position: absolute;
    bottom: 0px;
    background-color: white;
    padding: 30px;
    border-radius: 12px;
    width: 90%;
    max-width: 440px;
    z-index: 1001;
    height: 50%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.modal-header h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    width: 100%;
}

.close-btn {
    background: none;
    border: none;
    font-size: 28px;
    color: #999;
    cursor: pointer;
    padding: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    text-align: left;
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
}

.form-group input {
    width: 95%;
    padding: 12px;
    border: 1px solid #E5E5E5;
    border-radius: 8px;
    font-size: 16px;
}

.submit-btn {
    width: 100%;
    padding: 12px 0;
    background-color: #e4e6eb;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.submit-btn:hover {
    /* background-color: #1664D9; */
}

.Color1664D9 {
    background-color: #1664D9;
}

@media (max-width: 480px) {
    .header-title {
        font-size: 14px;
    }

    .welcome-text,
    .network-text {
        font-size: 6.41026vw;;
    }

    .feature-item {
        gap: 15px;
    }

    .feature-icon {
        width: 36px;
        height: 36px;
    }

    /* .modal-content {
        padding: 20px;
    } */
}
</style>