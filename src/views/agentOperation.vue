<template>
    <div class="container">
        <van-nav-bar :title="$t('计划跟随')" :border="false" left-arrow placeholder fixed @click-left="onClickLeft" />
        <img src="../assets/agent-banner.png" class="banner">
        <div class="top-section">
            <div class="desc">{{ $t('您当前的代运营ID') }}</div>
            <div class="id-list">
                <div class="id-item" v-for="(item, index) in userInfo.customId" :key="index">{{ item }}</div>
            </div>
            <van-button type="primary" color="#0866ff" size="large" @click="getOpenGensuiDialog">{{ $t('计划跟随') }}</van-button>
        </div>
        <div class="section">
            <div class="tab-wrap">
                <van-tabs v-model="jihuaIdx" title-inactive-color="#727073" title-active-color="#000000" color="#0866ff"
                    line-height="3px" line-width="90px">
                    <van-tab v-for="(item, index) in jihuaTabList" :title="item.name" :key="index">
                    </van-tab>
                </van-tabs>
            </div>
            <ul class="rule-wrap" v-show="jihuaIdx == 0">
                <li><span class="tag">1、</span><span class="text">{{ $t('followtext1') }}</span></li>
                <li><span class="tag">2、</span><span class="text">{{ $t('followtext2') }}</span></li>
                <li><span class="tag">3、</span><span class="text">{{ $t('followtext3') }}</span>
                </li>
                <li><span class="tag">4、</span><span class="text">{{ $t('followtext4') }}</span></li>
                <li><span class="tag">5、</span><span class="text">{{ $t('followtext5') }}</span></li>
            </ul>
            <div class="follow-list" v-show="jihuaIdx == 1">
                <!-- <van-list v-model="loading" :finished="finished" loading-text="加载中..." finished-text="没有更多了" @load="getList"> -->
                <div class="follow-item" v-for="(item, index) in gensuiList" :key="index">
                    <div class="item-content">
                        <van-image width="60" height="60" round :src="item.followedFbAvatar" />
                        <div class="info">
                            <div class="name">{{item.followedFbNickname}}</div>
                            <div class="desc">{{item.createTime}}</div>
                        </div>
                    </div>
                    <div class="status">{{ $t('跟随中') }}</div>
                </div>
                <div style="color: #969799;font-size: 0.37333rem;line-height: 1.33333rem;text-align: center;">{{ $t('没有更多了') }}</div>
                <!-- </van-list> -->
            </div>
        </div>

        <van-popup v-model="followShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{ $t('计划跟随') }}</div>
                <div class="form-wrap">
                    <div class="label">{{ $t('请输入代运营ID') }}</div>
                    <div class="input-wrap">
                        <van-field v-model="yunyingId" type="number" clearable />
                    </div>
                </div>
                <van-button type="info" color="#0866ff" size="large" :disabled="yunyingId ? false : true"
                    @click="getGensui">{{ $t('确认') }}</van-button>
            </div>
        </van-popup>
    </div>
</template>
<script>
import { getUserInfo, follow, followList } from '@/request/api';

export default {
    data() {
        return {
            nowyunyingId: "12345678",
            jihuaIdx: 0,
            loading: false,
            finished: false,
            followShow: false,
            yunyingId: "",
            userInfo: "",
            gensuiList: []
        };
    },
    mounted() {
        this.getUserDetail()
        this.getList()
    },
    computed: {
        jihuaTabList(){
            return [
                {
                    name: this.$t('运营规则')
                },
                {
                    name: this.$t('跟随记录')
                },
            ]
        }
    },
    methods: {
        // 用户信息
        getUserDetail() {
            const params = {};
            getUserInfo(params).then((res) => {
                if (res.code == 200) {
                    this.userInfo = res.data
                }
            });
        },
        getOpenGensuiDialog() {
            this.followShow = true
        },
        // 计划跟随
        getGensui() {
            if (!this.yunyingId) {
                this.$toast(this.$t('请输入运营ID'))
                return
            }
            const params = {
                followedAgentId: this.yunyingId
            };
            follow(params).then((res) => {
                if (res.code == 200) {
                    this.followShow = false
                    this.$toast.success(this.$t('跟随成功'))
                    this.getList()
                } else {
                    this.$toast(res.msg)
                }
            });
        },
        getList() {
            this.loading = true
            const params = {};
            followList(params).then((res) => {
                if (res.code == 200) {
                    this.loading = false;
                    this.finished = true
                    this.gensuiList = res.data
                }
            });
        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .banner {
        padding-top: 9.74359vw;
        width: 100%;
    }

    .top-section {
        padding: 8.20513vw 4.10256vw;
        border-bottom: 2.05128vw solid #F0F2F5;

        .desc {
            text-align: center;
            font-size: 4.10256vw;
            color: #727073;
            margin-bottom: 7.69231vw;
        }

        .id-list {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 7.69231vw;

            .id-item {
                margin-right: 1.02564vw;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 9.48718vw;
                height: 10.25641vw;
                background: #F0F2F5;
                border-radius: 1.02564vw;
                font-size: 6.15385vw;
                font-weight: 700;
            }
        }

        .van-button {
            border-radius: 16px;
        }
    }

    .section {
        .tab-wrap {
            position: relative;
            padding: 0 4.10256vw;

            &::after {
                content: "";
                position: absolute;
                width: 100%;
                height: 1px;
                background: #e5e5e5;
                bottom: 0;
                left: 0;
                right: 0;
            }
        }

        .rule-wrap {
            padding: 6.66667vw 7.69231vw;
            list-style: none;

            li {
                margin-bottom: 3.84615vw;
                display: flex;
                align-items: flex-start;
                font-size: 3.58974vw;
                color: #727073;
                line-height: 1.5;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .follow-list {
            .follow-item {
                padding: 4.10256vw 6.15385vw;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .item-content {
                    display: flex;
                    align-items: center;

                    .info {
                        margin-left: 3.07692vw;

                        .name {
                            font-size: 4.61538vw;
                            color: #000000;
                            font-weight: 500;
                        }

                        .desc {
                            font-size: 3.58974vw;
                            color: gray;
                        }
                    }
                }

                .status {
                    font-size: 3.33333vw;
                    color: #0866ff;
                }
            }
        }
    }

    .dialog-wrap {
        padding: 10.25641vw 4.10256vw 4.10256vw;
        background: #ffffff;

        .dialog-title {
            text-align: center;
            font-weight: 700;
            font-size: 6.41026vw;
        }

        .form-wrap {
            margin-bottom: 9.74359vw;
            margin-top: 10.25641vw;

            .label {
                font-size: 3.84615vw;
                color: #666666;
            }

            .input-wrap {
                display: flex;
                align-items: center;
                height: 16.92308vw;
                border-bottom: 1px solid #E5E5E5;

                /deep/ .van-cell {
                    flex: 1;
                    padding: 0;
                    font-size: 5.64103vw;
                    color: #000000;
                    font-weight: 700;

                    .van-field__label {
                        width: auto;
                        color: #000000;
                    }
                }
            }
        }

        /deep/ .van-button {
            border-radius: 16px;
        }
    }
}
</style>