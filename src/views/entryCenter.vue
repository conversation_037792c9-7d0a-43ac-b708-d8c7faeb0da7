<template>
    <div class="container">
        <div v-if="loader" class="loader"></div>
        <AdWelcome @custom-event="handleParentMethod" v-else></AdWelcome>
    </div>
</template>
<script>
import { register, isRegister } from '@/request/api'
import AdWelcome from '@/views/ad-welcome.vue'

export default {
    components: {
        AdWelcome
    },
    data() {
        return {
            loader: true
        };
    },
    mounted() {
        this.getRegister()
    },
    methods: {
        getRegister() {
            var that = this;
            const params = {
                encodedStr: this.$route.query.data
            };
            isRegister(params).then((res) => {
                if (res.code == 200) {
                    if (res.data.isRegister) {
                        //已注册
                        that.newRegirect(params);
                    } else {
                        if (res.data.lang == 'zh') {
                            // 检查是否为繁体中文地区
                            const region = res.data.region || '';
                            if (region === 'TW' || region === 'HK' || region === 'MO') {
                                window.localStorage.setItem('language', 'zh_TW')
                                that.$i18n.locale = 'zh_TW';
                            } else {
                                window.localStorage.setItem('language', 'zh_CN')
                                that.$i18n.locale = 'zh_CN';
                            }
                        } else {
                            window.localStorage.setItem('language', 'en_US')
                            that.$i18n.locale = 'en_US';
                        }
                        that.loader = false;
                    }
                }
            });
        },
        handleParentMethod(activationCode) {
            let params = {
                encodedStr: this.$route.query.data
            };
            params.invitationCode = activationCode;
            this.newRegirect(params);
        },
        newRegirect(params) {
            var that = this;
            that.loader = true;
            register(params).then((res) => {
                if (res.code == 200) {
                    window.localStorage.setItem('fbId', res.data.fbId)
                    if (res.data.lang == 'zh') {
                        // 检查是否为繁体中文地区
                        const region = res.data.region || '';
                        if (region === 'TW' || region === 'HK' || region === 'MO') {
                            window.localStorage.setItem('language', 'zh_TW')
                            that.$i18n.locale = 'zh_TW';
                        } else {
                            window.localStorage.setItem('language', 'zh_CN')
                            that.$i18n.locale = 'zh_CN';
                        }
                    } else {
                        window.localStorage.setItem('language', 'en_US')
                        that.$i18n.locale = 'en_US';
                    }
                    setTimeout(() => {
                        that.$router.replace('/advertiseCenter');
                        window.localStorage.setItem('index','index')
                    }, 1000);
                } else {
                    that.$toast.fail(res.msg)
                }
            });
        }
    }
}
</script>
<style lang="less" scoped>
.container {
    width: 100%;
    height: 100vh;
}

.loader {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10.25641vw;
    height: 5.12821vw;
    --c: no-repeat radial-gradient(farthest-side, #0866FF 93%, #0866FF);
    background: var(--c) 0 0, var(--c) 50% 0, var(--c) 100% 0;
    background-size: 2.05128vw 2.05128vw;
    animation: l4-0-34f238c0 1s linear infinite alternate;

    &::before {
        content: "";
        position: absolute;
        width: 2.05128vw;
        height: 3.07692vw;
        background: #0866FF;
        left: 0;
        top: 0;
        animation: l4-1-34f238c0 1s linear infinite alternate, l4-2-34f238c0 .5s cubic-bezier(0, 200, .8, 200) infinite;
    }
}

@keyframes l4-0-34f238c0 {
    0% {
        background-position: 0 100%, 50% 0, 100% 0;
    }

    8%,
    42% {
        background-position: 0 0, 50% 0, 100% 0;
    }

    50% {
        background-position: 0 0, 50% 100%, 100% 0;
    }

    58%,
    92% {
        background-position: 0 0, 50% 0, 100% 0;
    }

    100% {
        background-position: 0 0, 50% 0, 100% 100%;
    }
}

@keyframes l4-1-34f238c0 {
    100% {
        left: calc(100% - 8px);
    }
}

@keyframes l4-2-34f238c0 {
    100% {
        top: -.1px;
    }
}
</style>