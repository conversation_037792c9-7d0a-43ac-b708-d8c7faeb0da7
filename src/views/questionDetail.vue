<template>
  <div class="container">
    <van-nav-bar :title="$t('常见问题')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft"/>
    <div class="container_in" v-if="index == 0">
      <div class="question-title">{{ $t('什么是Facebook广告联盟？') }}</div>
      <div class="info">
        <div>{{ $t('notice1') }}</div>
        <div>{{ $t('notice2') }}</div>
        <div>{{ $t('notice3') }}</div>
      </div>
    </div>
    <div class="container_in" v-if="index == 1">
      <div class="question-title">{{ $t('平台收益与会员推荐权益') }}</div>
      <div class="info">
        <div class="notice-text">{{ $t('new-notice1') }}</div>
      </div>
    </div>
    <div class="container_in" v-if="index == 2">
      <div class="question-title">{{ $t('充值与提现') }}</div>
      <div class="info">
        <div class="notice-text">
          {{ $t('new-notice2') }}
        </div>
      </div>
    </div>
    <div class="container_in" v-if="index == 3">
      <div class="question-title">{{ $t('推广分润奖励') }}</div>
      <div class="notice-text">
        <div>{{ $t('new-notice3') }}</div>
      </div>
    </div>
    <div class="container_in" v-if="index == 4">
      <div class="question-title">{{ $t('会员推荐机制') }}</div>
      <div class="notice-text">
        <div>{{ $t('new-notice4') }}</div>
      </div>
    </div>
    <div class="container_in" v-if="index == 5">
      <div class="question-title">{{ $t('风险提示及合规说明') }}</div>
      <div class="notice-text">
        <div>{{ $t('new-notice5') }}</div>
      </div>
    </div>

  </div>
</template>
<script>
export default {
  data () {
    return {
      index: 0
    }
  },
  mounted () {
    this.index = this.$route.query.index
  },
  methods: {
    onClickLeft () {
      this.$router.go(-1)
    },
  },
}
</script>
<style lang="less" scoped>
.container {
  position: relative;

  .container_in {
    padding: 5.12821vw 4.10256vw;

    .question-title {
      font-size: 4.61538vw;
      font-weight: 500;
      margin-bottom: 5.12821vw;
      color: #000000;
    }


    .info {
      color: #65676a;
      font-size: 3.58974vw;
      line-height: 8.20513vw;
    }

    .notice-text {
      color: #65676a;
      white-space: pre-line;
      font-size: 3.58974vw;
      line-height: 8.20513vw;
    }
  }
}
</style>
