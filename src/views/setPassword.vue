<template>
    <div class="container">
        <van-nav-bar :title="$t('设置交易密码')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />
        <div class="section">
            <div class="page-title">{{ step == 1 ? $t('设置交易密码') : $t('确认交易密码') }}</div>
            <div class="page-desc">{{ step == 1 ? $t('请设置您的交易密码，确保交易安全') : $t('请确认您的交易密码，确保交易安全') }}</div>
            <van-password-input :value="value1" :length="6" :focused="showKeyboard" @focus="showKeyboard = true"
                v-if="step == 1" />
            <van-password-input :value="value2" :length="6" :focused="showKeyboard2" @focus="showKeyboard2 = true"
                v-else />
        </div>
        <div class="btn-wrap">
            <van-button type="primary" color="#0866ff" size="large" @click="getNextTwo"
                v-if="step == 1">{{$t('下一步')}}</van-button>
            <van-button type="primary" color="#0866ff" size="large" @click="getSubmit" v-else>{{$t('完成')}}</van-button>
        </div>
        <van-number-keyboard v-model="value1" :maxlength="6" :show="showKeyboard" @blur="showKeyboard = false"
            v-if="step == 1" />
        <van-number-keyboard v-model="value2" :maxlength="6" :show="showKeyboard2" @blur="showKeyboard2 = false"
            v-else />
    </div>
</template>
<script>
import { editInfo } from '@/request/api'
export default {
    data() {
        return {
            showKeyboard: false,
            value1: "",
            showKeyboard2: false,
            value2: "",
            step: 1,
        };
    },
    mounted() {

    },
    watch: {
        value1(val) {
            if (val.length == 6) {
                this.showKeyboard = false
            }
        },
        value2(val) {
            if (val.length == 6) {
                this.showKeyboard2 = false
            }
        }
    },
    methods: {
        onClickLeft() {
            this.$router.go(-1)
        },
        getNextTwo() {
            if (this.value1.length != 6) {
                this.$toast(this.$t('请输入6位交易密码'));
                return
            }
            this.step = 2;
        },
        getSubmit() {
            var that = this
            if (this.value2.length != 6) {
                this.$toast(this.$t('请输入6位交易密码'));
                return
            }
            if (this.value1 != this.value2) {
                this.$toast(this.$t('两次交易密码不一致'));
                return
            }
            const params = {
                password: this.value2,
            };
            editInfo(params).then((res) => {
                if (res.code == 200) {
                    this.$toast.success(this.$t('设置成功'));
                    setTimeout(() => {
                        that.$router.go(-1)
                    }, 1000);
                }else{
                    this.$toast(res.msg)
                }
            });
        }
    },
};
</script>
<style lang="less" scoped>
.container {
    position: relative;
    height: calc(100vh);
    flex: 1;
    overflow: auto;

    .section {
        padding: 4.61538vw 11.79487vw;
        text-align: center;

        .page-title {
            font-size: 6.15385vw;
            font-weight: 500;
            color: #000000;
            line-height: 1;
            margin-bottom: 4.10256vw;
        }

        .page-desc {
            font-size: 3.84615vw;
            margin-bottom: 5.64103vw;
        }

        /deep/ .van-password-input {
            margin: 0;

            .van-hairline--surround {
                &::after {
                    border-color: #d1d3d9 !important;
                }
            }
        }
    }

    .btn-wrap {
        position: absolute;
        bottom: 4.10256vw;
        left: 4.10256vw;
        right: 4.10256vw;
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);

        .van-button {
            width: 100%;
            border-radius: 16px;
        }
    }
}
</style>