<template>
  <div class="container">
    <van-nav-bar :title="$t('计划管理')" :border="false" fixed placeholder left-arrow @click-left="onClickLeft"
      z-index="9" />
    <div class="tab-wrap">
      <van-tabs title-inactive-color="#727073" title-active-color="#000000" color="#0866ff" line-height="3px"
        @click="getTab" line-width="54px" v-model="titleTabIdx">
        <van-tab v-for="(item, index) in tabList" :title="item.name" :key="index">
        </van-tab>
      </van-tabs>
    </div>
    <div class="listmian">
      <van-list v-model="loading" offset="30" :finished="finished" :loading-text="$t('加载中...')"
        :finished-text="$t('没有更多了')" @load="getList">
        <div class="plan-item" @click="getLink(item)" v-for="(item, index) in projectList" :key="index">
          <div class="push-tag" v-if="item.planType === '1'">{{ $t('固定任务') }}</div>
          <div class="push-tag" v-else-if="item.isOfficial == 'Y'">{{ $t('官方推送') }}</div>
          <div class="title-line">
            <span class="title">{{ $t('计划编号') }}:{{ item.orderNo }}</span>
            <span class="status">{{ item.status | filterType(that) }}</span>
            <!-- <span class="status">待投放</span> -->
          </div>
          <div class="plan-content" v-if="item.app">
            <div class="banner">
              <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
                <van-swipe-item v-for="(item, index) in item.app.image.split(',')" :key="index">
                  <img :src="item" alt="">
                </van-swipe-item>
              </van-swipe>
            </div>
            <div class="app-content">
              <div class="app-title">
                <div class="logo">
                  <img :src="item.app.logo" alt="">
                </div>
                <div class="app-title-wrap">
                  <div class="app-name">{{ item.app.name }}</div>
                  <div class="app-desc">{{ item.app.type }}</div>
                </div>
              </div>
              <div class="app-tag">
                <div class="tag">{{ $t('视频') }}</div>
                <div class="tag">{{ $t('落地页') }}</div>
              </div>
            </div>
          </div>
          <div class="plan-text" v-if="item.app">{{ item.app.description }}</div>
          <div class="put-wrap" v-if="item.status == 0">
            <div class="put-item"><span class="label">{{ $t('最低投放') }}</span><span class="value">$ {{ item.lowInvAmount
                }}</span>
            </div>
            <div class="put-item"><span class="label">{{ $t('最高投放') }}</span><span class="value">$ {{ item.highInvAmount
                }}</span>
            </div>
            <!-- VIP计划库显示收益比例（待投放状态下用户需要知道收益率） -->
            <div class="put-item" v-if="item.planType === '1'">
              <span class="label">{{ $t('收益率') }}</span>
              <span class="value profit-rate">{{ item.profitRate || 0 }}%</span>
            </div>
            <!-- VIP计划库显示广告投放时间 -->
            <div class="put-item" v-if="item.planType === '1'">
              <span class="label">{{ $t('广告投放时间') }}</span>
              <span class="value">{{ item.adDuration || '--' }}</span>
            </div>
            <!-- VIP计划库显示投放会员等级要求 -->
            <div class="put-item" v-if="item.planType === '1'">
              <span class="label">{{ $t('投放要求') }}</span>
              <span class="value">{{ formatVipLevelRequirement(item.minVipLevel) }}</span>
            </div>
          </div>

          <div class="put-wrap" v-else>
            <div class="put-item"><span class="label">{{ $t('投放金额') }}</span><span class="value">$ {{ item.putAmount
                }}</span></div>
            <!-- VIP任务不显示收益情况，但可以显示投放进度 -->
            <div class="put-item"><span class="label">{{ $t('投放进度') }}</span>
              <div class="progress">
                <van-progress :percentage="Number(item.putProgress)" color="#0866ff" :show-pivot="false"
                  track-color="#ffffff" stroke-width="5px" />
                <span class="progress-text">{{ item.putProgress }}%</span>
              </div>
            </div>
          </div>
          <!-- VIP计划库不显示创建时间 -->
          <div class="title-line mt-2" v-if="item.planType !== '1'">
            <span class="title" style="font-size: 14px;">{{ $t('创建时间') }}: {{ item.distriTime }}</span>
          </div>
          <div class="btn-wrap" v-if="item.status == 0">
            <!-- VIP计划库按钮状态控制 -->
            <van-button
              v-if="item.planType === '1'"
              round
              type="info"
              :color="canUserInvest(item) ? '#0866ff' : '#a0c4ff'"
              size="small"
              @click.stop="handleVipPlanClick(item)">
              {{ getInvestButtonText(item) }}
            </van-button>
            <!-- 普通计划按钮 -->
            <van-button
              v-else
              round
              type="info"
              color="#0866ff"
              size="small"
              @click.stop="getTouFangDialog(item)">
              {{ $t('投放') }}
            </van-button>
          </div>
        </div>
      </van-list>
    </div>

    <van-popup v-model="touFangShow" position="bottom" round>
      <div class="dialog-wrap">
        <div class="dialog-title">{{ $t('投放金额') }}</div>
        <div class="form-wrap">
          <div class="input-wrap">
            <!-- <span class="label">$</span> -->
            <van-field v-model="amount" label="$" :placeholder="$t('自定义金额')" clearable />
          </div>
          <div class="form-tips">
            <span class="count-money">{{ $t('可用余额') }} ${{ walletDetail.availableFunds }}</span>
            <span class="all-text" @click="amount = walletDetail.availableFunds">{{ $t('全部') }}</span>
          </div>
          <div class="coupon-item" @click="youhuiShow = true"><span class="title">{{ $t('优惠券') }}</span>
            <div class="status">
              <span class="status-text" :class="couponId ? 'active' : ''">{{ couponId ? $t('已使用') : $t('未使用') }}</span>
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
        <van-button round type="info" color="#0866ff" size="large" :disabled="amount ? false : true"
          @click="getTouFang">{{ $t('实时投放') }}</van-button>
      </div>
    </van-popup>
    <van-popup v-model="youhuiShow" position="bottom" :style="{ height: '100%' }">
      <div class="dialog-wrap2">
        <van-nav-bar :title="$t('使用优惠券')" :border="false" placeholder left-arrow @click-left="youhuiShow = false" />

        <div class="tab-wrap">
          <van-tabs title-inactive-color="#727073" title-active-color="#000000" color="#0866ff" line-height="3px"
            @click="getyouhuiTab" line-width="70px" v-model="youHuiidx">
            <van-tab v-for="(item, index) in youhuiTabList" :title="item.name" :key="index">
            </van-tab>
          </van-tabs>
        </div>
        <div class="coupon-list">
          <van-list v-model="youhuiLoading" :finished="youhuiFinished" :loading-text="$t('加载中...')"
            :finished-text="$t('没有更多了')" @load="getYouhuiList">
            <div class="coupon-item used" v-for="(item, index) in YouhuiList" :key="index"
              @click="getSelectCoupon(item)">
              <div class="item-left" :class="item.status == 0 ? 'activebgblue' : ''">
                <div class="coupon-type">{{ $t('折扣券') }}</div>
                <div class="tag" :class="item.status == 0 ? 'activecolorblue' : ''">{{ item.tag }}</div>
              </div>
              <div class="item-content">
                <div class="title-line">
                  <div class="title" :class="item.status == 0 ? 'activecolorblack' : ''">{{ item.couponName }}
                  </div><span class="text" :class="item.status == 0 ? 'activecolorblue' : ''">{{ item.status ==
                    0 ? $t('未使用') : $t('已使用') }}</span>
                </div>
                <div class="name" :class="item.status == 0 ? 'activecolorblack' : ''">{{ item.couponDesc }}</div>
                <div class="time" :style="item.status == 0 ? 'opacity:.6' : ''">{{ item.expireTime }}{{ $t('到期') }}
                </div>
              </div>
            </div>
          </van-list>
        </div>
      </div>
    </van-popup>




    <van-popup v-model="followShow" round style="width: 90%;">
      <div class="dialog-wrap">
        <div class="dialog-title" style="font-size: 5vw;font-weight: 400;margin-bottom: 2vw;">{{ $t('请认证后再进行投放') }}
        </div>
        <van-button type="info" color="#0866ff" size="large" @click="toAuthentication" >{{intMin}}{{ $t('秒后自动跳转') }}</van-button>
      </div>
    </van-popup>








  </div>
</template>
<script>
import { PlanList, userCoupons, jihuaput, property, getUserInfo } from '@/request/api'
export default {
  data() {
    return {
      titleTabIdx: 0,
      page: 1,
      pageSize: 20,
      userInfo: {},
      projectList: [],
      loading: false,
      finished: false,
      touFangShow: false,
      amount: null,
      youhuiShow: false,
      youHuiidx: 0,
      youHuipage: 1,
      YouhuiList: [],
      youhuiLoading: false,
      youhuiFinished: false,
      followShow: false,
      time:null,
      youhuiStatus: 0,
      walletDetail: "",
      orderDetail: "",
      couponId: "",
      intMin:"",
      that: this,
    };
  },
  mounted() {
    this.getUserDetail();
    // this.getWallet()
    // this.getList()
  },
  computed: {
    tabList() {
      return [
        {
          name: this.$t('全部')
        },
        {
          name: this.$t('待投放')
        },
        {
          name: this.$t('匹配中')
        },
        {
          name: this.$t('投放中')
        },
        {
          name: this.$t('投放失败')
        },
        {
          name: this.$t('投放完成')
        },
      ]
    },
    youhuiTabList() {
      return [
        {
          name: this.$t('全部')
        },
        {
          name: this.$t('未使用')
        },
        {
          name: this.$t('已使用')
        },
      ]
    }
  },
  filters: {
    filterType(val, that) {
      // 获取当前语言环境
      const currentLang = that.$i18n.locale;

      switch (val) {
        case '0':
          return currentLang === 'en' ? 'Pending' : that.$t('待投放')
        case '1':
          return currentLang === 'en' ? 'Matching' : that.$t('匹配中')
        case '2':
          return currentLang === 'en' ? 'Active' : that.$t('投放中')
        case '3':
          return currentLang === 'en' ? 'Failed' : that.$t('投放失败')
        case '4':
          return currentLang === 'en' ? 'Settlement' : that.$t('等待结算')
        case '5':
          return currentLang === 'en' ? 'Completed' : that.$t('结算完成')
        default:
          break;
      }
    }
  },
  methods: {
    // 格式化VIP等级要求显示
    formatVipLevelRequirement(minVipLevel) {
      if (minVipLevel === null || minVipLevel === undefined) {
        return 'All';
      }
      if (minVipLevel === 0) {
        return 'All';
      }
      return ">="+`VIP${minVipLevel}`;
    },

    // 判断用户是否可以投放VIP计划
    canUserInvest(item) {
      if (item.planType !== '1') {
        return true; // 普通计划都可以投放
      }

      // 获取用户VIP等级
      const userVipLevel = this.userInfo?.vip || 0;
      const requiredVipLevel = item.minVipLevel || 0;

      return userVipLevel >= requiredVipLevel;
    },

    // 获取投放按钮文本
    getInvestButtonText(item) {
      if (item.planType !== '1') {
        return this.$t('投放');
      }

      if (this.canUserInvest(item)) {
        return this.$t('可投放');
      } else {
        const requiredLevel = item.minVipLevel || 0;
        if (requiredLevel === 0) {
          return this.$t('可投放');
        }
        return `VIP${requiredLevel}` +this.$t('可投放');
      }
    },

    // 处理VIP计划点击事件
    handleVipPlanClick(item) {
      if (this.canUserInvest(item)) {
        // 用户VIP等级符合要求，正常投放
        this.getTouFangDialog(item);
      } else {
        // 用户VIP等级不符合要求，跳转到VIP会员页面
        // this.$router.push('/vip');
      }
    },

    // 资金信息
    getWallet() {
      const params = {};
      property(params).then((res) => {
        if (res.code == 200) {
          this.walletDetail = res.data
        }
      });
    },
    getUserDetail() {
      const params = {};
      let that=this;
      getUserInfo(params).then((res) => {
        if (res.code == 200) {
          that.userInfo = res.data;
          if (!that.userInfo.authenticationStatus || that.userInfo.authenticationStatus == '0') {
            that.followShow=true;
            that.intMin=3;
            that.time=setInterval(function(){
              if(that.intMin==0){
               that.toAuthentication();
              }else{
                that.intMin=that.intMin-1;
              }

            },1000);
          } else {
            that.getWallet();
            // that.getList();
          }
        }
      });
    },

    toAuthentication(){
      clearInterval(this.time);
      this.$router.push({path: '/Authentication'})
    },
    // 优惠券tab点击
    getyouhuiTab() {
      this.youHuipage = 1
      this.YouhuiList = []
      this.getYouhuiList()
    },
    // 优惠券列表
    getYouhuiList() {
      this.youhuiLoading = true;
      const params = {
        pageNum: this.youHuipage++,
        pageSize: this.pageSize,
        status: this.youHuiidx == 0 ? "" : this.youHuiidx - 1,
      };
      userCoupons(params).then((res) => {
        if (res.code == 200) {
          this.youhuiLoading = false;
          this.YouhuiList = this.YouhuiList.concat(res.rows);
          if (this.YouhuiList.length >= res.total) {
            this.youhuiFinished = true;
          }
        }
      });
    },
    // 选择优惠券
    getSelectCoupon(item) {
      if (item.status != 0) {
        return
      }
      this.couponId = item.id
      this.youhuiShow = false
    },
    // 打开投放弹窗
    getTouFangDialog(item) {
      this.couponId = ""
      this.amount = null
      this.orderDetail = item
      this.touFangShow = true
    },
    // 投放
    getTouFang() {
      if (!this.amount || this.amount == 0) {
        this.$toast(this.$t('请输入正确的投放金额'));
        return
      }
      if (Number(this.amount) < Number(this.orderDetail.lowInvAmount)) {
        this.$toast(this.$t('不可小于最低投放金额'));
        return
      }
      if (Number(this.amount) > Number(this.orderDetail.highInvAmount)) {
        this.$toast(this.$t('不可大于最高投放金额'));
        return
      }
      const params = {
        orderNo: this.orderDetail.orderNo,
        putAmount: this.amount,
        couponId: this.couponId,
      };
      jihuaput(params).then((res) => {
        if (res.code == 200) {
          this.$toast.success(this.$t('投放成功'));
          this.touFangShow = false
          this.getWallet()
          this.getTab()
        } else {
          this.$toast(res.msg);
        }
      });

    },
    // 跳转计划详情
    getLink(item) {
      this.$router.push({
        path: '/planDetail',
        query: {
          orderNo: item.id
        }
      })
    },
    // 当前页面返回
    onClickLeft() {
      window.location.href = "hybrid://doAction?params=close"
      // this.$router.go(-1)
      // window.history.go(-2)
    },
    // 计划列表tab点击
    getTab() {
      this.page = 1
      this.projectList = []
      this.loading = true
      this.finished = false
      this.getList()

    },
    // 计划管理列表
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.page++,
        pageSize: this.pageSize,
        status: this.titleTabIdx == 0 ? "" : this.titleTabIdx - 1,
      };
      PlanList(params).then((res) => {
        if (res.code == 200) {
          this.loading = false;
          this.projectList = this.projectList.concat(res.rows);
          if (res.rows.length >= res.total) {
            this.finished = true;
          }
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  .tab-wrap {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 1px;
      background: #e5e5e5;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }

  .listmian {
    .van-list {
      padding: 5.12821vw 4.10256vw;

      .plan-item {
        border: 1px solid #E4E6EB;
        border-radius: 2.05128vw;
        padding: 3.58974vw 4.10256vw;
        margin-bottom: 3.84615vw;

        .push-tag {
          display: flex;
          width: max-content;
          font-size: 3.07692vw;
          color: #fff;
          background: #0866ff;
          padding: 1.02564vw 3.07692vw;
          border-radius: 1.53846vw;
          margin-bottom: 1.53846vw;
        }

        .title-line {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 10px;

          .title {
            font-size: 3.84615vw;
            font-weight: 500;
            color: #000000;
            line-height: 5.64103vw;
          }

          .status {
            font-size: 2.5641vw;
            color: #0265d1;
            background: #EBF5FF;
            padding: 0 3.07692vw;
            line-height: 5.12821vw;
            border-radius: 5.12821vw;
            white-space: nowrap;
            max-width: 25vw;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }
        }

        .plan-content {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-top: 5.12821vw;

          .banner {
            width: 44.10256vw;
            height: 24.61538vw;
            flex-shrink: 0;

            .my-swipe {
              width: 100%;
              height: 100%;

              .van-swipe-item {
                img {
                  width: 100%;
                  height: 100%;
                  border-radius: 2.05128vw;
                }
              }
            }
          }

          .app-content {
            flex: 1;
            margin-left: 3.84615vw;

            .app-title {
              display: flex;
              align-items: center;

              .logo {
                width: 8.20513vw;
                height: 8.20513vw;
                flex-shrink: 0;
                margin-right: 3.07692vw;

                img {
                  width: 100%;
                  height: 100%;
                  border-radius: 2.05128vw;
                }
              }

              .app-title-wrap {
                color: #000000;

                .app-name {
                  font-size: 3.33333vw;
                  font-weight: 500;
                }

                .app-desc {
                  font-size: 2.30769vw;
                }
              }
            }

            .app-tag {
              display: flex;
              align-items: center;
              flex-wrap: wrap;

              .tag {
                min-width: 14.35897vw;
                height: 5.12821vw;
                text-align: center;
                line-height: 5.12821vw;
                background: #F0F2F5;
                font-size: 2.5641vw;
                color: #000000;
                margin-right: 2.05128vw;
                margin-top: 2.05128vw;
              }
            }
          }
        }

        .plan-text {
          font-size: 3.07692vw;
          line-height: 4.61538vw;
          color: #000000;
          margin-top: 3.84615vw;
        }

        .put-wrap {
          background: #F0F2F5;
          border-radius: 3.07692vw;
          padding: 3.84615vw 4.61538vw;
          margin-top: 3.33333vw;

          .put-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 3.07692vw;
            color: #000000;

            &:first-child {
              margin-bottom: 3.58974vw;
            }

            &:not(:first-child) {
              margin-top: 3.58974vw;
            }

            .value {
              font-weight: 500;
              text-align: right;
              flex: 1;
              margin-left: 3.84615vw;
            }

            .progress {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 33.33333vw;

              /deep/ .van-progress {
                flex: 1;
              }

              .progress-text {
                font-size: 3.07692vw;
                color: #000000;
                font-weight: 500;
                margin-left: 1.02564vw;
              }
            }
          }
        }

        .btn-wrap {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-top: 3.33333vw;

          /deep/ .van-button {
            height: 9.23077vw;
            min-width: 27.69231vw;
          }
        }
      }
    }
  }



  .dialog-wrap {
    padding: 10.25641vw 4.10256vw 4.10256vw;
    background: #ffffff;

    .dialog-title {
      text-align: center;
      font-weight: 700;
      font-size: 6.41026vw;
      margin-bottom: 12.82051vw;
    }

    .form-wrap {
      .input-wrap {
        display: flex;
        align-items: center;
        height: 16.92308vw;
        border-bottom: 1px solid #E5E5E5;

        /deep/ .van-cell {
          flex: 1;
          padding: 0;
          font-size: 5.64103vw;
          color: #000000;
          font-weight: 700;

          .van-field__label {
            width: auto;
            color: #000000;
          }
        }
      }

      .form-tips {
        display: flex;
        align-items: center;
        margin-top: 3.84615vw;
        font-size: 3.07692vw;

        .count-money {
          display: flex;
          align-items: center;
          margin-right: 2.5641vw;
          color: #000000;
        }

        .all-text {
          color: #0866ff;
          cursor: pointer;
        }
      }

      .coupon-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #F0F2F5;
        border-radius: 3.07692vw;
        height: 12.82051vw;
        padding: 0 4.61538vw;
        color: #000000;
        font-size: 3.07692vw;
        margin-top: 4.61538vw;

        .status {
          display: flex;
          align-items: center;

          .status-text {
            margin-right: 1.53846vw;
            font-size: 3.07692vw;
            font-weight: 500;
            color: #adadad;
          }

          .active {
            color: #0866ff;
          }
        }
      }
    }

    /deep/ .van-button {
      margin-top: 4.61538vw;
      border-radius: 16px;
    }
  }

  .dialog-wrap2 {
    background: #ffffff;
    min-height: 100%;

    .coupon-list {
      flex: 1;
      overflow: hidden;
      padding: 5.89744vw 4.10256vw;

      .coupon-item {
        display: flex;
        align-items: center;
        height: 24.61538vw;
        margin-bottom: 3.84615vw;

        .item-left {
          background: rgb(173, 173, 173);
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          flex-shrink: 0;
          width: 30.76923vw;
          height: 100%;
          border-radius: 4.10256vw 0 0 4.10256vw;


          .coupon-type {
            color: #ffffff;
            font-size: 5.12821vw;
            font-weight: 600;
            line-height: 1;
            margin-bottom: 2.05128vw;
          }

          .tag {
            color: #adadad;
            padding: 1.02564vw 3.07692vw;
            border-radius: 7.69231vw;
            background: #ffffff;

            font-size: 2.5641vw;
            line-height: 1;
          }
        }

        .item-content {
          position: relative;
          display: flex;
          flex-direction: column;
          flex: 1;
          background: #EDF5FE;
          border-radius: 0 4.10256vw 4.10256vw 0;
          padding: 4.10256vw 6.15385vw 4.10256vw 5.12821vw;
          height: -webkit-fill-available;
          line-height: 1;

          &::after {
            content: "";
            position: absolute;
            width: 6.66667vw;
            height: 6.66667vw;
            border-radius: 50%;
            right: -3.33333vw;
            top: 8.97436vw;
            z-index: 1;
            background: #ffffff;
          }

          .title-line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.53846vw;

            .title {
              color: #8e8e8e;
              font-size: 5.12821vw;
              font-weight: 600;
            }

            .text {
              color: #8e8e8e;
              opacity: .8;
              font-size: 2.5641vw;
            }
          }

          .name {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.53846vw;
            font-size: 4.10256vw;
            color: #8e8e8e;
          }

          .time {
            opacity: .2;
            font-size: 2.5641vw;
            color: #000000;
            margin-top: 3.07692vw;
          }
        }

        .activecolorblue {
          color: #0866ff !important;
        }

        .activebgblue {
          background: #0866ff !important;
        }

        .activecolorblack {
          color: #000000 !important;
        }
      }
    }
  }
}
</style>
