<template>
    <div class="container">
        <van-nav-bar :title="$t('计划详情')" fixed :border="false" placeholder left-arrow @click-left="onClickLeft" />
        <div class="container_nei">
            <div class="plan-status-wrap">
                <div class="status-content">
                    <div class="title">{{ detail.status | filterType(that) }}</div>
                    <div class="desc">{{ detail.status | filterDesc(that) }}</div>
                </div>
                <van-icon name="passed" color="#ffffff" size="40px" v-if="detail.status == 4 || detail.status == 5" />
                <van-icon name="underway-o" color="#ffffff" size="40px"
                    v-if="detail.status == 0 || detail.status == 1" />
                <van-icon name="miniprogram-o" color="#ffffff" size="40px" v-if="detail.status == 2" />
                <van-icon name="warn-o" color="#ffffff" size="40px" v-if="detail.status == 3" />
            </div>
            <div class="section">
                <div class="section-title">{{ $t('投放产品') }}</div>
                <div class="put-goods">
                    <div class="goods-content"><img alt="" class="logo" :src="detail.app ? detail.app.logo : ''">
                        <div class="goods-info">
                            <div class="goods-name">{{ detail.app ? detail.app.name : '' }}</div>
                            <div class="goods-desc">{{ detail.app ? detail.app.type : '' }}</div>
                            <div class="goods-sub">{{ detail.app ? detail.app.company : '' }}</div>
                        </div>
                    </div>
                    <div class="link" @click="getLink">{{ $t('查看详情') }}</div>
                </div>
            </div>
            <div class="section">
                <div class="section-title">
                    <div class="title">{{ $t('数据概览') }}</div>
                    <div class="desc"><span>{{ $t('数据每30s更新一次') }}</span>
                        <svg class="svg-icon" aria-hidden="true" @click="getDetail" @animationend='inAnimation = false'
                            :class="inAnimation ? 'rotating' : ''">
                            <use xlink:href="#icon-refresh">
                                <symbol fill="none" viewBox="0 0 13 13" id="icon-refresh">
                                    <path
                                        d="M4.81 2.549c.436-.13.918-.216 1.45-.216a4.35 4.35 0 0 1 4.35 4.35 4.35 4.35 0 0 1-4.35 4.351 4.35 4.35 0 0 1-3.618-6.769M4.188 2.67l1.45-1.666M4.188 2.67l1.69 1.234"
                                        stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                                </symbol>
                            </use>
                        </svg>
                    </div>
                </div>
                <div class="data-section">
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">$ {{ detail.putAmount }}</div>
                        </div>
                        <div class="data-title">{{ $t('投放金额') }}
                            <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-1">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-1">
                                        <path
                                            d="M8.672 14.33c0 1.29.99 2.33 2.22 2.33h2.51c1.07 0 1.94-.91 1.94-2.03 0-1.22-.53-1.65-1.32-1.93l-4.03-1.4c-.79-.28-1.32-.71-1.32-1.93 0-1.12.87-2.03 1.94-2.03h2.51c1.23 0 2.22 1.04 2.22 2.33M12 6v12"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path d="M15 22H9c-5 0-7-2-7-7V9c0-5 2-7 7-7h6c5 0 7 2 7 7v6c0 5-2 7-7 7Z"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg>
                        </div>
                    </div>
                    <div class="data-item progress-item">
                        <div class="number-line">
                            <div class="progress-wrap">
                                <div class="number">{{ detail.putProgress }}%</div>
                                <van-progress :percentage="Number(detail.putProgress)" color="#0866ff"
                                    :show-pivot="false" track-color="#ebedf0" />
                            </div>
                        </div>
                        <div class="data-title">{{ $t('投放进度') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-2">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-2">
                                        <path
                                            d="M6.73 19.7c.82-.88 2.07-.81 2.79.15l1.01 1.35c.81 1.07 2.12 1.07 2.93 0l1.01-1.35c.72-.96 1.97-1.03 2.79-.15 1.78 1.9 3.23 1.27 3.23-1.39V7.04C20.5 3.01 19.56 2 15.78 2H8.22C4.44 2 3.5 3.01 3.5 7.04V18.3c0 2.67 1.46 3.29 3.23 1.4ZM8 7h8M9 11h6"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">$ {{ detail.spentAmount }}</div>
                        </div>
                        <div class="data-title">{{ $t('已消耗') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-3">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-3">
                                        <path
                                            d="M9.5 13.75c0 .97.75 1.75 1.67 1.75h1.88c.8 0 1.45-.68 1.45-1.53 0-.91-.4-1.24-.99-1.45l-3.01-1.05c-.59-.21-.99-.53-.99-1.45 0-.84.65-1.53 1.45-1.53h1.88c.92 0 1.67.78 1.67 1.75M12 7.5v9"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2" stroke="#292D32"
                                            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M17 3v4h4M22 2l-5 5" stroke="#292D32" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">$ {{ detail.waitSpentAmount }}</div>
                        </div>
                        <div class="data-title">{{ $t('待消耗') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-4">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-4">
                                        <path
                                            d="M9.5 13.75c0 .97.75 1.75 1.67 1.75h1.88c.8 0 1.45-.68 1.45-1.53 0-.91-.4-1.24-.99-1.45l-3.01-1.05c-.59-.21-.99-.53-.99-1.45 0-.84.65-1.53 1.45-1.53h1.88c.92 0 1.67.78 1.67 1.75M12 7.5v9"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path
                                            d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2M22 6V2h-4M17 7l5-5"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">{{ detail.showCount }}</div>
                        </div>
                        <div class="data-title">{{ $t('展示数') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-5">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-5">
                                        <path
                                            d="M9.03 14c-.39-.57-.61-1.26-.61-2 0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58-1.6 3.58-3.58 3.58"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path
                                            d="M17.56 5.58c-1.69-1.2-3.59-1.85-5.56-1.85-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68 3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">{{ detail.clickCount }}</div>
                        </div>
                        <div class="data-title">{{ $t('点击数') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-6">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-6">
                                        <path
                                            d="m17.29 4.14-.07 3.79c-.01.52.32 1.21.74 1.52l2.48 1.88c1.59 1.2 1.33 2.67-.57 3.27l-3.23 1.01c-.54.17-1.11.76-1.25 1.31l-.77 2.94c-.61 2.32-2.13 2.55-3.39.51l-1.76-2.85c-.32-.52-1.08-.91-1.68-.88l-3.34.17c-2.39.12-3.07-1.26-1.51-3.08l1.98-2.3c.37-.43.54-1.23.37-1.77L4.28 6.43c-.59-1.9.47-2.95 2.36-2.33l2.95.97c.5.16 1.25.05 1.67-.26l3.08-2.22c1.66-1.2 2.99-.5 2.95 1.55ZM21.91 22l-3.03-3.03"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">$ {{ detail.adsIncome }}</div>
                        </div>
                        <div class="data-title">{{ $t('广告收入') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-7">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-7">
                                        <path
                                            d="M8 11.4c0 .77.6 1.4 1.33 1.4h1.5c.64 0 1.16-.55 1.16-1.22 0-.73-.32-.99-.79-1.16l-2.4-.84c-.48-.17-.8-.43-.8-1.16 0-.67.52-1.22 1.16-1.22h1.5c.74.01 1.34.63 1.34 1.4M10 12.85v.74M10 6.41v.78"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path
                                            d="M9.99 17.98A7.99 7.99 0 1 0 9.99 2a7.99 7.99 0 0 0 0 15.98ZM12.98 19.88c.9 1.27 2.37 2.1 4.05 2.1 2.73 0 4.95-2.22 4.95-4.95 0-1.66-.82-3.13-2.07-4.03"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                    <div class="data-item">
                        <div class="number-line">
                            <div class="number">$ {{ detail.grossProfit }}</div>
                        </div>
                        <div class="data-title">{{ $t('利润') }} <svg class="svg-icon" aria-hidden="true">
                                <use xlink:href="#icon-data-8">
                                    <symbol fill="none" viewBox="0 0 24 24" id="icon-data-8">
                                        <path
                                            d="M10.752 16.86v2.03c0 1.72-1.6 3.11-3.57 3.11-1.97 0-3.58-1.39-3.58-3.11v-2.03c0 1.72 1.6 2.94 3.58 2.94 1.97 0 3.57-1.23 3.57-2.94Z"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path
                                            d="M10.75 14.11c0 .5-.14.96-.38 1.36-.59.97-1.8 1.58-3.2 1.58-1.4 0-2.61-.62-3.2-1.58-.24-.4-.38-.86-.38-1.36 0-.86.4-1.63 1.04-2.19.65-.57 1.54-.91 2.53-.91.99 0 1.88.35 2.53.91.66.55 1.06 1.33 1.06 2.19Z"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path
                                            d="M10.752 14.11v2.75c0 1.72-1.6 2.94-3.57 2.94-1.97 0-3.58-1.23-3.58-2.94v-2.75c0-1.72 1.6-3.11 3.58-3.11.99 0 1.88.35 2.53.91.64.56 1.04 1.34 1.04 2.2ZM22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path
                                            d="M2 10.5v-2c0-2.72 1.64-4.62 4.19-4.94.26-.04.53-.06.81-.06h9c.26 0 .51.01.75.05C19.33 3.85 21 5.76 21 8.5v1.45h-2.08c-.56 0-1.07.22-1.44.6-.42.41-.66 1-.6 1.63.09 1.08 1.08 1.87 2.16 1.87H21v1.45c0 3-2 5-5 5h-2.5"
                                            stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </symbol>
                                </use>
                            </svg></div>
                    </div>
                </div>
            </div>
            <div class="section">
                <div class="section-title">{{ $t('投放内容') }}</div>
                <div class="banner-wrap"><img alt="" :src="detail.app ? detail.plan.mainImg : ''"></div>
                <div class="put-desc">{{ detail.app ? detail.app.description : '' }}</div>
                <div class="form-cell-wrap" v-if="detail.plan">
                    <div class="cell-item" v-for="(item, index) in detail.plan.content" :key="index">
                        <div class="label">{{ item.title }}</div>
                        <div class="content">{{ item.value }}</div>
                    </div>
                </div>
            </div>
            <div class="section">
                <div class="section-title">{{ $t('投放规则') }}</div>
                <div class="form-cell-wrap" v-if="detail.plan">
                    <div class="cell-item" v-for="(item, index) in detail.plan.rule" :key="index">
                        <div class="label">{{ item.title }}</div>
                        <div class="content">{{ item.value }}</div>
                    </div>
                </div>
            </div>
            <div class="section" v-if="detail.planType !== '1'">
                <div class="section-title">{{ $t('用户定向') }}</div>
                <div class="form-cell-wrap" v-if="detail.plan">
                    <div class="cell-item" v-for="(item, index) in detail.plan.target" :key="index">
                        <div class="label">{{ item.title }}</div>
                        <div class="content">{{ item.value }}</div>
                    </div>
                </div>
            </div>
            <div class="section" v-if="detail.planType !== '1'">
                <div class="section-title">
                    <div class="title">{{ $t('创建时间') }}</div>
                    <div class="desc"><span>{{ detail.distriTime }}</span></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { PlanDetail } from '@/request/api'
export default {
    data() {
        return {
            id: "",
            detail: {},
            timer1: null,
            inAnimation: true,
            that: this,
        };
    },
    mounted() {
        this.id = this.$route.query.orderNo
        this.getDetail()
        this.timer1 = setInterval(() => {
            this.getDetail();
        }, 30000);
    },
    filters: {
        filterDesc(val, that) {
            switch (val) {
                case '0':
                    return that.$t('待支付投放费用')
                case '1':
                    return that.$t('正在匹配广告份额')
                case '2':
                    return that.$t('广告正在推广宣传中')
                case '3':
                    return that.$t('广告投放失败')
                case '4':
                    return that.$t('投放完成')
                case '5':
                    return that.$t('投放完成')
                default:
                    break;
            }
        },
        filterType(val, that) {
            switch (val) {
                case '0':
                    return that.$t('待投放')
                case '1':
                    return that.$t('匹配中')
                case '2':
                    return that.$t('投放中')
                case '3':
                    return that.$t('投放失败')
                case '4':
                    return that.$t('等待结算')
                case '5':
                    return that.$t('结算完成')
                default:
                    break;
            }
        }
    },
    beforeDestroy() {
        if (this.timer1) {
            clearInterval(this.timer1);
        }
        this.timer1 = null;
    },
    methods: {
        getDetail() {
            PlanDetail(this.id).then((res) => {
                if (res.code == 200) {
                    this.inAnimation = true;
                    this.detail = res.data
                    this.detail.plan.content = JSON.parse(this.detail.plan.content)
                    this.detail.plan.rule = JSON.parse(this.detail.plan.rule)
                    this.detail.plan.target = JSON.parse(this.detail.plan.target)
                    console.log(this.detail)
                }
            });
        },
        onClickLeft() {
            this.$router.go(-1)
        },
        getLink() {
            this.$router.push({
                path: '/goodsDetail',
                query: {
                    orderNo: this.id
                }
            })
        },
    },
};
</script>
<style lang="less" scoped>
.container {

    .container_nei {
        padding: 0px 4.10256vw 5.12821vw;

        .plan-status-wrap {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #0866ff;
            border-radius: 3.07692vw;
            padding: 6.15385vw 7.69231vw;
            line-height: 1;

            .status-content {
                flex: 1 1 0%;
                color: #ffffff;
                margin-right: 3.84615vw;

                .title {
                    font-size: 6.15385vw;
                    font-weight: 500;
                    margin-bottom: 5.12821vw;
                }

                .desc {
                    font-size: 3.84615vw;
                    line-height: 1.2;
                }
            }
        }

        .section {
            margin-top: 6.15385vw;

            .section-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 4.10256vw;
                font-size: 4.10256vw;

                .title {
                    font-size: 4.61538vw;
                    font-weight: 500;
                    color: #000000;
                }

                .desc {
                    display: flex;
                    align-items: center;
                    font-size: 3.07692vw;
                    color: #666;

                    .svg-icon {
                        color: #0866ff;
                        font-size: 3.33333vw;
                        margin-left: 1.02564vw;
                        cursor: pointer;
                    }
                }
            }

            .put-goods {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-radius: 2.05128vw;
                background: rgb(240, 242, 245);
                padding: 3.07692vw;

                .goods-content {
                    display: flex;
                    align-items: center;
                    flex: 1 1 0%;
                    margin-right: 2.5641vw;

                    .logo {
                        width: 18.4615vw;
                        height: 18.4615vw;
                        margin-right: 4.61538vw;
                        border-radius: 4.61538vw;
                    }

                    .goods-info {
                        flex: 1 1 0%;
                        display: flex;
                        flex-direction: column;

                        .goods-name {
                            font-size: 3.84615vw;
                            font-weight: 500;
                            color: #000000;
                            margin-bottom: 2.05128vw;
                        }

                        .goods-desc {
                            font-size: 2.5641vw;
                            color: #000000;
                            margin-bottom: 2.82051vw;
                        }

                        .goods-sub {
                            font-size: 2.5641vw;
                            color: #969799;
                        }
                    }
                }

                .link {
                    font-size: 2.82051vw;
                    color: #0866ff;
                    cursor: pointer;
                }
            }

            .data-section {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 2.05128vw;

                .data-item {
                    padding: 3.84615vw 3.58974vw 3.84615vw 4.61538vw;
                    background: #ffffff;
                    border-radius: 2.05128vw;
                    border: 1px solid #C9CCD1;

                    .number-line {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .progress-wrap {
                            flex: 1;
                            margin-right: 7.69231vw;
                            margin-bottom: 1.28205vw;
                        }

                        .number {
                            font-size: 4.61538vw;
                            font-weight: 700;
                            color: #000000;
                        }
                    }

                    .data-title {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 3.07692vw;
                        color: #000000;

                        .svg-icon {
                            font-size: 6.15385vw;
                        }
                    }
                }
            }

            .banner-wrap {
                height: 51.53846vw;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .put-desc {
                font-size: 3.07692vw;
                color: #000000;
                margin: 5.89744vw 0 5.12821vw;
            }

            .form-cell-wrap {
                padding: 3.58974vw 4.61538vw;
                border-radius: 3.07692vw;
                background: #F0F2F5;

                .cell-item {
                    display: flex;
                    align-items: flex-start;
                    justify-content: space-between;
                    color: #000000;
                    margin-bottom: 5.12821vw;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .label {
                        flex-shrink: 0;
                        font-size: 3.07692vw;
                        width: 20.51282vw;
                    }

                    .content {
                        font-size: 3.07692vw;
                        flex: 1;
                        margin-left: 10.25641vw;
                        text-align: right;
                        word-break: break-all;
                    }
                }
            }
        }
    }
}
</style>