<template>
    <div class="container">
        <van-nav-bar :title="$t('外汇承兑')" :border="false" left-arrow fixed @click-left="onClickLeft" />
        <div class="header-wrap">
            <div class="current"><span class="label">{{$t('当前承兑')}}</span>
                <div class="change-city">
                    <van-image width="10.25641vw" height="7.17949vw" />
                    <svg class="svg-icon change-icon change-icon" aria-hidden="true">
                        <use xlink:href="#icon-change">
                            <symbol fill="none" viewBox="0 0 24 24" id="icon-change">
                                <path d="m20.5 14.99-5.01 5.02M3.5 14.99h17M3.5 9.01l5.01-5.02M20.5 9.01h-17"
                                    stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                                    stroke-linecap="round" stroke-linejoin="round"></path>
                            </symbol>
                        </use>
                    </svg>
                    <van-image width="10.25641vw" height="7.17949vw" />
                </div>
            </div>
            <div class="select-shop"><span class="label">{{$t('承兑商选择')}}</span>
                <div class="select-list">
                    <div class="select-item" @click="chengdui = 1; chengduiShow = true">
                        <img src="../assets/gerenicon.png" alt="">
                        <div class="item-content">
                            <div class="title">{{$t('个人承兑商')}}</div>
                            <div class="desc"><span class="text">{{$t('限额')}}</span><span class="num">$5,000 -
                                    $500,000</span><span class="text">{{$t('以内')}}</span></div>
                        </div>
                    </div>
                    <div class="select-item" @click="chengdui = 2; chengduiShow = true">
                        <img src="../assets/qiyeicon.png" alt="">
                        <div class="item-content">
                            <div class="title">{{$t('企业承兑商')}}</div>
                            <div class="desc"><span class="text">{{$t('限额')}}</span><span class="num">$500,001 -
                                    $1,000,000</span><span class="text">{{$t('以内')}}</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="record-list-wrap">
            <div class="record-title">{{$t('动帐记录')}}</div>
            <div class="record-list">
                <van-list v-model="loading" :finished="finished" :loading-text="$t('加载中...')" :finished-text="$t('没有更多了')"
                    @load="getList">
                    <div class="record-item" @click="getDetail">
                        <div class="record-icon">
                            <img src="../assets/gerenicon.png" alt="">
                        </div>
                        <div class="record-content">
                            <div class="title-line"><span class="title">{{$t('个人外汇承兑')}}</span><span class="money">+
                                    $1,000,000</span></div>
                            <div class="desc-line"><span class="time">29/07/2024
                                    21:12:56</span><span class="status status-1">{{$t('进行中')}}</span></div>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>

        <van-popup v-model="chengduiShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{$t('承兑码获取')}}</div>
                <img src="../assets/foreign-banner.png" alt="" width="343" class="banner">
                <p class="text">{{$t('请联系在线客服')}}</p>
                <p class="text">{{ chengdui == 1 ? $t('获取本次个人承兑码') : $t('获取本次企业承兑码') }}</p>
                <div class="btn-wrap">
                    <van-button round plain type="info" color="#0866ff" size="large"
                        @click="getLink('customerService')">{{$t('联系客服')}}</van-button>
                    <van-button round type="info" color="#0866ff" size="large"
                        @click="chengduiShow = false; tianxieShow = true;">{{$t('立即填写')}}</van-button>
                </div>
            </div>
        </van-popup>

        <van-popup v-model="tianxieShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{ chengdui == 1 ? $t('填写个人承兑码') : $t('填写企业承兑码') }}</div>
                <div class="form-wrap">
                    <div class="label">{{ chengdui == 1 ? $t('请输入个人承兑码') : $t('请输入企业承兑码') }}</div>
                    <div class="input-wrap">
                        <van-field v-model="chengduima" clearable />
                    </div>
                </div>
                <van-button type="info" color="#0866ff" size="large" :disabled="chengduima ? false : true"
                    @click="getChengdui">{{$t('确认')}}</van-button>
            </div>
        </van-popup>

        <van-popup v-model="detailShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{$t('承兑详情')}}</div>
                <div class="exchange-type-line">
                    <div class="type-name">
                        <img src="../assets/gerenicon.png" alt="">
                        <img src="../assets/qiyeicon.png" alt="">
                        <span class="name">01</span>
                    </div>
                    <span class="status status-1">{{$t('进行中')}}</span>
                </div>
                <div class="exchange-info-item">
                    <span class="label">TRC20:</span>
                    <span class="text">TSB314nSWSMj273Mfn7i...</span>
                </div>
                <div class="exchange-info-item progress">
                    <span class="label">{{$t('剩余额度')}}:</span>
                    <div class="progress-wrap">
                        <div class="progress-out">
                            <van-progress :percentage="30" color="#0866ff" :show-pivot="false" track-color="#ebedf0"
                                stroke-width="8px" />
                            <span class="num">100%</span>
                        </div>
                        <div class="progress-money">
                            <span class="current">$1,000,000</span>
                            <span> / 1,000,000</span>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: false,
            finished: false,
            page: 1,
            pageSize: 20,
            chengduiShow: false,
            chengdui: 0,
            tianxieShow: false,
            detailShow: false,
            chengduima: ""
        };
    },
    mounted() {

    },
    methods: {
        getLink(val) {
            this.$router.push({
                path: val
            })
        },
        getDetail(){
            this.detailShow = true
        },
        getChengdui(){

        },
        getList() {

        },
        onClickLeft() {
            this.$router.go(-1)
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    /deep/ .van-nav-bar {
        background: transparent;
        color: #ffffff;

        .van-icon {
            color: #ffffff;
        }

        .van-nav-bar__title {
            color: #ffffff;
        }
    }

    .header-wrap {
        background: #0866FF;
        padding: 15.38462vw 6.66667vw 7.17949vw;

        .current {
            display: flex;
            align-items: center;
            margin-bottom: 7.17949vw;

            .label {
                font-size: 4.10256vw;
                color: #ffffff;
                font-weight: 500;
                line-height: 1;
            }

            .change-city {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                margin-left: 3.84615vw;
                color: #ffffff;

                .change-icon {
                    font-size: 6.15385vw;
                    margin: 0 5.12821vw;
                }
            }
        }

        .select-shop {
            .label {
                font-size: 4.10256vw;
                color: #ffffff;
                font-weight: 500;
                line-height: 1;
            }

            .select-list {
                margin-top: 5.12821vw;

                .select-item {
                    margin-bottom: 4.61538vw;
                    display: flex;
                    align-items: center;
                    padding: 4.61538vw 7.17949vw;
                    border-radius: 4.10256vw;
                    background: #ffffff;

                    img {
                        width: 9.23077vw;
                        height: 9.23077vw;
                    }

                    .item-content {
                        margin-left: 5.64103vw;

                        .title {
                            font-size: 3.84615vw;
                            color: #000000;
                            font-weight: 500;
                            line-height: 1;
                        }

                        .desc {
                            margin-top: 2.05128vw;
                            font-size: 2.5641vw;
                            line-height: 1;

                            .text {
                                color: #000000;
                            }

                            .num {
                                margin: 0 1.02564vw;
                                color: #0866ff;
                            }
                        }
                    }
                }
            }
        }
    }

    .record-list-wrap {
        padding: 7.69231vw 6.66667vw;

        .record-title {
            font-size: 4.61538vw;
            color: #000000;
            font-weight: 500;
            line-height: 1;
            margin-bottom: 9.74359vw;
        }

        .record-list {
            .record-item {
                margin-bottom: 7.17949vw;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .record-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    width: 12.30769vw;
                    height: 12.30769vw;
                    border-radius: 50%;
                    background: #F0F2F5;
                    font-size: 6.15385vw;

                    img {
                        width: 24px;
                        height: 24px;
                    }
                }

                .record-content {
                    flex: 1;
                    margin-left: 3.84615vw;
                    height: 12.30769vw;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;

                    .title-line {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        color: #000000;

                        .title {
                            font-size: 3.84615vw;
                            flex: 1;
                            font-weight: 500;
                            margin-right: 5.12821vw;
                        }

                        .money {
                            flex-shrink: 0;
                            font-size: 3.58974vw;
                            font-weight: 700;
                        }
                    }

                    .desc-line {
                        display: flex;
                        -webkit-box-align: center;
                        -webkit-align-items: center;
                        align-items: center;
                        -webkit-box-pack: justify;
                        -webkit-justify-content: space-between;
                        justify-content: space-between;

                        .time {
                            font-size: 3.07692vw;
                            color: #969799;
                        }

                        .status {
                            font-size: 3.07692vw;
                            color: #969799;
                        }

                        .status-1 {
                            color: #69bc36;
                        }
                    }
                }
            }
        }
    }

    .dialog-wrap {
        padding: 10.25641vw 4.10256vw 4.10256vw;
        background: #ffffff;

        .dialog-title {
            text-align: center;
            font-weight: 700;
            font-size: 6.41026vw;
        }

        .form-wrap {
            margin-bottom: 9.74359vw;
            margin-top: 10.25641vw;

            .label {
                font-size: 3.84615vw;
                color: #666666;
            }

            .input-wrap {
                display: flex;
                align-items: center;
                height: 16.92308vw;
                border-bottom: 1px solid #E5E5E5;

                /deep/ .van-cell {
                    flex: 1;
                    padding: 0;
                    font-size: 5.64103vw;
                    color: #000000;
                    font-weight: 700;

                    .van-field__label {
                        width: auto;
                        color: #000000;
                    }
                }
            }
        }

        .banner {
            display: block;
            margin: 5.12821vw auto;
        }

        .text {
            font-size: 4.61538vw;
            font-weight: 500;
            color: #000000;
            text-align: center;
            padding: 0 4.10256vw;
        }

        /deep/ .van-button {
            border-radius: 16px;
        }

        .btn-wrap {
            margin-top: 9.23077vw;
            display: flex;
            justify-content: space-between;

            /deep/ .van-button {
                border-radius: 16px;
                width: 48%;
            }
        }

        .exchange-type-line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 7.69231vw;
            margin-top: 7.69231vw;

            .type-name {
                display: flex;
                align-items: center;
                flex: 1;

                img {
                    width: 6.15385vw;
                    height: 6.15385vw;
                }

                .name {
                    font-size: 4.10256vw;
                    font-weight: 500;
                    margin-left: 2.5641vw;
                }
            }

            .status {
                font-size: 3.07692vw;
                color: #969799;
            }

            .status-1 {
                color: #69bc36;
            }
        }

        .exchange-info-item {
            display: flex;
            align-items: center;
            font-size: 3.84615vw;
            margin-bottom: 3.84615vw;

            .label {
                color: #969799;
                margin-right: 5.12821vw;
            }

            .text {
                color: #000000;
                font-weight: 500;
                font-size: 3.84615vw;
            }

            .progress-wrap {
                flex: 1;
                padding-right: 7.69231vw;

                .progress-out {
                    display: flex;
                    align-items: center;

                    .van-progress{
                        flex: 1;
                    }

                    .num {
                        font-size: 3.33333vw;
                        color: #0866ff;
                        margin-left: 2.5641vw;
                    }
                }

                .progress-money {
                    font-size: 3.33333vw;
                    color: #969799;
                    margin-top: 4px;

                    .current {
                        color: #0866ff;
                    }
                }
            }
        }

        .progress {
            align-items: flex-start;
        }
    }
}
</style>