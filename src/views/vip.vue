<template>
    <div class="container">
        <van-nav-bar :title="$t('vip特权')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />

        <main>

            <div data-v-d6e1139f="" class="vip-page">
                <div class="current-vip not-vip" v-if="!userInfo.vip">
                    <div class="vip-header">
                        <div class="not-vip-tip"><i
                                class="van-badge__wrapper van-icon van-icon-diamond-o"></i><span>{{$t('开通VIP享受更多特权')}}</span>
                        </div>
                    </div>
                </div>


                <div  class="current-vip" v-else>
                    <div  class="vip-header">
                        <div  class="vip-info">
                            <div  class="van-image van-image--round"
                                style="width: 12vw; height: 12vw;"><img
                                    src="https://facebooks.s3.ap-east-1.amazonaws.com/38fa7e6cd9784e4db8d0072f69299481.jpg"
                                    class="van-image__img" style="object-fit: cover;"><!----><!----></div>
                            <div  class="info-content">
                                <div  class="level">{{thisVip.levelName}}</div>
                                <div  class="vip-desc">{{$t('您已经是尊贵的VIP用户')}}</div>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="vip-levels" v-for="(item, index) in vipLevelList" :key="index">
                    <div class="vip-level-item">
                        <div class="level-header">
                            <div class="level-left">
                                <div class="van-image van-image--round" style="width: 10vw; height: 10vw;"><img
                                        :src="item.imgUrl[index]"
                                        class="van-image__img" style="object-fit: cover;"></div>
                                <div class="level-info">
                                    <div class="level-name">{{ item.levelName }}</div>
                                    <div class="level-tag">
                                        <div class="vip-tag">{{ item.levelName }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="level-price"><span class="currency">$</span><span
                                    class="amount">{{ item.buyingPrice }}</span>
                            </div>
                        </div>
                        <div class="level-content">
                            <div class="benefit-items">
                                <div class="benefit-item">
                                    <div class="benefit-icon"><i
                                            class="van-badge__wrapper van-icon van-icon-balance-o"></i>
                                    </div>
                                    <div class="benefit-info">
                                        <div class="benefit-label">{{$t('分佣比例')}}</div>
                                        <div class="benefit-value highlight">{{ item.fybl }}%</div>
                                    </div>
                                </div>
                                <div class="benefit-item">
                                    <div class="benefit-icon"><i
                                            class="van-badge__wrapper van-icon van-icon-friends-o"></i>
                                    </div>
                                    <div class="benefit-info">
                                        <div class="benefit-label">{{$t('下级限额')}}</div>
                                        <div class="benefit-value">
                                            {{ item.subordinatesLimit == '0' ? $t('不限制') : item.subordinatesLimit }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="privileges">
                                <div class="privilege-item"><i
                                        class="van-badge__wrapper van-icon van-icon-success check-icon"></i><span>{{$t(item.levelName+'-1')}}</span>
                                </div>
                                <div class="privilege-item"><i
                                        class="van-badge__wrapper van-icon van-icon-success check-icon"></i><span>{{$t(item.levelName+'-2')}}</span>
                                </div>
                                <div class="privilege-item" v-if="item.levelName !== 'VIP1'"><i
                                  class="van-badge__wrapper van-icon van-icon-success check-icon"></i><span>{{$t(item.levelName+'-3')}}</span>
                                </div>
                            </div>
                            <div class="action">
                                <button type="button" @click="openVip(item)"
                                    class="van-button  van-button--normal van-button--block  "
                                    :class="getButtonClass(item)"
                                    :disabled="isButtonDisabled(item)">
                                    <div class="van-button__content"><span
                                            class="van-button__text">{{ getButtonText(item) }}</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </main>


        <van-dialog v-model="show" :title="$t('确认升级')" @confirm="confirm" show-cancel-button :cancelButtonText="$t('取消')"
            :confirmButtonText="$t('确认支付')" confirmButtonColor="rgb(78, 124, 220)">
            <div class="van-dialog__message">{{$t('确认升级到')}}:{{ openVipDate.levelName }} {{$t('吗')}} </br>{{$t('开通费用')}}：${{ openVipDate.buyingPrice }}
            </div>
        </van-dialog>

        <van-popup v-model="passShow" position="bottom">
            <div class="dialog-wrap" :style="showKeyboard == true ? 'padding-bottom: 310px;' : ''">
                <div class="dialog-title">{{ $t('交易密码') }}</div>
                <div class="desc">{{ $t('请输入您的交易密码') }}</div>
                <van-password-input :value="password" :length="6" :focused="showKeyboard"
                    @focus="showKeyboard = true" />
                <div class="btn-wrap">
                    <van-button type="primary" color="#0866ff" size="large" :disabled="password.length != 6 ? true : false" @click="getPassVerify">{{ $t('确认')
                        }}</van-button>
                </div>
                <van-number-keyboard v-model="password" :maxlength="6" :show="showKeyboard"
                    @blur="showKeyboard = false" />
            </div>
        </van-popup>
    </div>
</template>
<script>
import { getVipLevel, getUserInfo, openVip } from '@/request/api'
import VIP1 from "@/assets/VIP1.jpg";
import VIP2 from "@/assets/VIP2.jpg";
import VIP3 from "@/assets/VIP3.jpg";
import VIP4 from "@/assets/VIP4.jpg";



export default {
    data() {
        return {
            activeName: "",
            userInfo: {},
            vipLevelList: [],
            show: false,
            openVipDate: {},
            passShow: false,
            showKeyboard: true,
            password: "",
            thisVip:{},//当前vip

        };
    },
    mounted() {
        this.init();
    },
    watch: {
        password(val) {
            if (val.length == 6) {
                this.showKeyboard = false
            }
        },
    },
    methods: {
        init() {
            this.getUserDetail();

        },
        onClickLeft() {
            this.$router.back()
        },

        confirm() {
            this.passShow = true;
            console.log('5');
        },

        // 用户信息
        getUserDetail() {
            const params = {};
            getUserInfo(params).then((res) => {
                if (res.code == 200) {
                    this.userInfo = res.data
                }
                this.getVipLevel();
            });
        },

        getVipLevel() {
            getVipLevel().then((res) => {
                console.log(res);
                if (res.code == 200) {
                    //this.ptLevel=res.rows.filter(item=>0===item.levelValue)[0];
                    // console.log(res.rows.filter(item=>0===item.levelValue));
                    this.vipLevelList = res.rows.filter(item => 0 != item.levelValue);
                    if (this.vipLevelList) {
                        this.vipLevelList.forEach((item) => {
                            // 从 commissionRate1 字段取值，去除小数点并拼接 %
                            item.fybl = parseFloat(item.commissionRate1);

                            if(item.levelValue==this.userInfo.vip){
                                this.thisVip=item;
                            }
                            item.imgUrl=new Array();
                            item.imgUrl[0]=VIP1;
                            item.imgUrl[1]=VIP2;
                            item.imgUrl[2]=VIP3;
                            item.imgUrl[3]=VIP4;
                        });
                    }
                }
            });
        },

        openVip(data) {
            if (!this.userInfo.password) {
                this.$router.push({path: 'setPassword'});
                return
            }
            // 如果是当前等级或已拥有的等级，不执行任何操作
            if (this.isButtonDisabled(data)) {
                return;
            }
            this.openVipDate = data;
            this.show = true;
        },

        /**
         * 判断按钮是否应该被禁用
         * @param {Object} item VIP等级项
         * @returns {Boolean} 是否禁用
         */
        isButtonDisabled(item) {
            const currentVip = this.userInfo.vip || 0;
            // 如果当前VIP等级大于等于该项的等级，则禁用按钮
            return currentVip >= item.levelValue;
        },

        /**
         * 获取按钮的CSS类
         * @param {Object} item VIP等级项
         * @returns {String} CSS类名
         */
        getButtonClass(item) {
            const currentVip = this.userInfo.vip || 0;
            if (currentVip >= item.levelValue) {
                return 'is-current'; // 已拥有或当前等级
            }
            return 'van-button--primary'; // 可购买
        },

        /**
         * 获取按钮显示的文本
         * @param {Object} item VIP等级项
         * @returns {String} 按钮文本
         */
        getButtonText(item) {
            const currentVip = this.userInfo.vip || 0;
            if (currentVip === item.levelValue) {
                return this.$t('当前等级');
            } else if (currentVip > item.levelValue) {
                return this.$t('已拥有');
            }
            return this.$t('立即开通');
        },


        getPassVerify() {
            //充值vip
            this.openVipDate.password = this.password;
            openVip(this.openVipDate).then((res) => {
                if (res.code == 200) {
                    this.init();
                    this.$toast.success(this.$t('vip开通成功'));
                } else {
                    this.$toast(this.$t(res.msg));
                }
                this.passShow = false
            });

        },

    },
};
</script>
<style lang="less" scoped>
main {
    flex: 1;
    overflow: auto;
}

.vip-page {
    min-height: 100%;
    padding: 4vw;
    background-color: #f5f7fa;

    .current-vip {
        background: linear-gradient(135deg, #4e7cdc, #3c5aa6);
        border-radius: 4vw;
        padding: 6vw 4vw;
        margin-bottom: 4vw;
        box-shadow: 0 4px 12px #4e7cdc33;
    }

    .current-vip.not-vip {
        padding: 4vw;

        .not-vip-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2vw;
            color: #fff;
            font-size: 4vw;
        }
    }

    .vip-levels {
        .vip-level-item {
            background: #fff;
            border-radius: 3vw;
            padding: 4vw;
            margin-bottom: 4vw;
            box-shadow: 0 4px 16px #00000014;
            transition: all .3s ease;

            .level-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 4vw;
                padding-bottom: 4vw;
                border-bottom: 1px solid #f5f5f5;

                .level-left {
                    display: flex;
                    align-items: center;
                    gap: 3vw;

                    .level-info {

                        font-size: 4.5vw;
                        font-weight: 700;
                        color: #333;
                        margin-bottom: 1vw;

                        .level-tag {
                            .vip-tag {
                                display: inline-block;
                                padding: .8vw 2vw;
                                background: linear-gradient(135deg, #ffb800, #ff8a00);
                                border-radius: 1vw;
                                color: #fff;
                                font-size: 2.8vw;
                                font-weight: 600;
                                line-height: 1;
                                box-shadow: 0 2px 4px #ffa80033;
                            }
                        }

                    }
                }

                .currency {
                    font-size: 3.5vw;
                    color: #f44336;
                }

                .amount {
                    font-size: 6vw;
                    font-weight: 700;
                    color: #f44336;
                }

            }

            .level-content {
                .benefit-items {
                    display: flex;
                    gap: 3vw;
                    margin: 3vw 0;
                    padding: 3vw;
                    background: #f9f9f9;
                    border-radius: 2vw;

                    .benefit-item {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        gap: 2vw;


                        .benefit-info {
                            flex: 1;

                            .benefit-label {
                                font-size: 3vw;
                                color: #666;
                                margin-bottom: .5vw;
                            }

                            .benefit-info .benefit-value.highlight {
                                color: #4e7cdc;
                            }

                            .benefit-value {
                                font-size: 3.5vw;
                                color: #333;
                                font-weight: 700;
                            }
                        }

                        .benefit-icon {
                            width: 6vw;
                            height: 6vw;
                            border-radius: 1.5vw;
                            background: #f5f7fa;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .van-icon {
                                font-size: 4vw;
                                color: #4e7cdc;
                            }
                        }
                    }
                }

                .privileges {
                    margin: 4vw 0;
                    padding: 3vw;
                    background: #f9f9f9;
                    border-radius: 2vw;

                    .privilege-item {
                        display: flex;
                        align-items: center;
                        gap: 2vw;
                        margin-bottom: 2vw;
                        font-size: 3.2vw;
                        color: #666;

                        .check-icon {
                            color: #4e7cdc;
                        }
                    }
                }
            }
        }
    }


    .van-button--primary {
        color: #fff;
        background: #095de5;
        border: var(--van-button-border-width) solid var(--van-button-primary-border-color);
        border-radius: 5px;
    }

    .van-dialog__message {
        color: var(--van-text-color);
        flex: 1;
        max-height: var(--van-dialog-message-max-height);
        padding: 26px var(--van-dialog-message-padding);
        overflow-y: auto;
        font-size: var(--van-dialog-message-font-size);
        line-height: var(--van-dialog-message-line-height);
        white-space: pre-wrap;
        text-align: center;
        word-wrap: break-word;
        -webkit-overflow-scrolling: touch;
    }




}

.dialog-wrap {
    padding: 10.25641vw 4.10256vw 4.10256vw;
    background: #ffffff;

    .dialog-title {
        text-align: center;
        font-weight: 700;
        font-size: 6.41026vw;
        margin-bottom: 12.82051vw;
    }

    .desc {
        text-align: center;
        font-size: 3.84615vw;
        color: #666666;
        margin-bottom: 7.69231vw;
    }

    .form-wrap {
        .label {
            font-size: 3.84615vw;
            color: #666666;
        }

        .input-wrap {
            display: flex;
            align-items: center;
            height: 16.92308vw;
            border-bottom: 1px solid #E5E5E5;

            /deep/ .van-cell {
                flex: 1;
                padding: 0;
                font-size: 5.64103vw;
                color: #000000;
                font-weight: 700;

                .van-field__label {
                    width: auto;
                    color: #000000;
                }
            }
        }

        .form-tips {
            display: flex;
            align-items: center;
            margin-top: 3.84615vw;
            font-size: 3.07692vw;

            .count-money {
                display: flex;
                align-items: center;
                margin-right: 2.5641vw;
                color: #000000;
            }

            .all-text {
                color: #0866ff;
                cursor: pointer;
            }
        }
    }

    .text {
        font-size: 4.61538vw;
        font-weight: 500;
        color: #000000;
        text-align: center;
        padding: 0 4.10256vw;
    }

    /deep/ .van-button {
        margin-top: 4.61538vw;
        border-radius: 16px;
    }

    .content-wrap {
        margin-top: 12.82051vw;

        .content-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 4.35897vw;
            margin-bottom: 8.71795vw;

            .label {
                color: #666666;
                min-width: 20.51282vw;
            }

            .text {
                flex: 1;
                flex-shrink: 0;
                margin-left: 2.5641vw;
                text-align: right;
                color: #000000;
                font-weight: 500;
                word-break: break-all;
            }
        }
    }
}

.is-current {
    background: #f5f5f5;
    border-color: #ddd;
    color: #999;
    cursor: not-allowed;
    opacity: .8;
    border-radius: 0.13333rem;
    border: var(--van-button-border-width) solid var(--van-button-primary-border-color);
}

.vip-info {
    display: flex;
    align-items: center;
    gap: 4vw;
    .info-content{
        color: #fff;
        .level {
            font-size: 5vw;
            font-weight: 700;
            margin-bottom: 2vw;
            display: flex;
            align-items: center;
            gap: 2vw;
        }
        .vip-desc {
            font-size: 3.2vw;
            opacity: .8;
        }
    }
}
</style>
