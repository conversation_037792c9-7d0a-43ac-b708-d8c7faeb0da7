<template>
    <div class="container">
        <van-nav-bar :title="$t('我的钱包')" :border="false" placeholder left-arrow fixed @click-left="onClickLeft" />
        <div class="section">
            <div class="title-line">
                <div class="title"><span>{{ $t('总资产') }}</span>
                    <svg class="svg-icon" aria-hidden="true" v-if="isLock" @click="isLock = false">
                        <use xlink:href="#icon-eye">
                            <symbol fill="none" viewBox="0 0 24 24" id="icon-eye">
                                <path
                                    d="M15.58 12c0 1.98-1.6 3.58-3.58 3.58S8.42 13.98 8.42 12s1.6-3.58 3.58-3.58 3.58 1.6 3.58 3.58Z"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round"></path>
                                <path
                                    d="M12 20.27c3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19-2.29-3.6-5.58-5.68-9.11-5.68-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68Z"
                                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                </path>
                            </symbol>
                        </use>
                    </svg>
                    <svg class="svg-icon" aria-hidden="true" v-if="!isLock" @click="isLock = true">
                        <use xlink:href="#icon-close-eye">
                            <symbol fill="none" viewBox="0 0 24 24" id="icon-close-eye">
                                <path d="m14.53 9.47-5.06 5.06a3.576 3.576 0 1 1 5.06-5.06Z" stroke="currentColor"
                                    stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path
                                    d="M17.82 5.77C16.07 4.45 14.07 3.73 12 3.73c-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19.79 1.24 1.71 2.31 2.71 3.17M8.42 19.53c1.14.48 2.35.74 3.58.74 3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19-.33-.52-.69-1.01-1.06-1.47"
                                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                </path>
                                <path d="M15.51 12.7a3.565 3.565 0 0 1-2.82 2.82M9.47 14.53 2 22M22 2l-7.47 7.47"
                                    stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                </path>
                            </symbol>
                        </use>
                    </svg>
                </div><span class="tag">USD</span>
            </div>
            <div class="money">${{ isLock ? Number(walletDetail.balance).toFixed(2) : "******" }}</div>
            <div class="money-content">
                <div class="money-item"><span class="label">{{ $t('可用余额') }}</span><span class="text">${{
            isLock ? walletDetail.availableFunds : "******" }}</span></div>
                <div class="money-item"><span class="label">{{ $t('待消耗') }}</span><span class="text">${{
            isLock ? walletDetail.waitSpentAmount : "******" }}</span></div>
                <div class="money-item"><span class="label">{{ $t('待结算') }}</span><span class="text">${{ isLock ?
            walletDetail.toBeSettled : "******"
                        }}</span></div>
            </div>
            <div class="btn-wrap">
                <van-button plain type="primary" round color="#0866ff" size="large" @click="getTixian">{{ $t('提现') }}</van-button>
                <van-button type="primary" round color="#0866ff" size="large"
                    @click="rechargeShow = true">{{ $t('充值') }}</van-button>
            </div>
            <div class="link-list">
                <div class="link-item" @click="getBindCard">
                    <div class="link-title"><svg class="svg-icon" aria-hidden="true">
                            <use xlink:href="#icon-card-pos">
                                <symbol fill="none" viewBox="0 0 24 24" id="icon-card-pos">
                                    <path d="M3.93 15.88 15.88 3.93M11.101 18.279l1.2-1.2M13.793 15.589l2.39-2.39"
                                        stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path
                                        d="m3.601 10.239 6.64-6.64c2.12-2.12 3.18-2.13 5.28-.03l4.91 4.91c2.1 2.1 2.09 3.16-.03 5.28l-6.64 6.64c-2.12 2.12-3.18 2.13-5.28.03l-4.91-4.91c-2.1-2.1-2.1-3.15.03-5.28ZM2 21.998h20"
                                        stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"></path>
                                </symbol>
                            </use>
                        </svg><span class="label">{{ $t('设置提现方式') }}</span></div><van-icon name="arrow" />
                </div>
                <!-- <div class="link-item" @click="getLink('foreignExchange')">
                    <div class="link-title"><svg class="svg-icon" aria-hidden="true">
                            <use xlink:href="#icon-waihui">
                                <symbol fill="none" viewBox="0 0 24 24" id="icon-waihui">
                                    <path d="M7.5 8.34h7.4c.89 0 1.6.72 1.6 1.6v1.77" stroke="currentColor"
                                        stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round"></path>
                                    <path d="M9.19 6.66 7.5 8.34l1.69 1.69M16.5 15.66H9.1c-.89 0-1.6-.72-1.6-1.6v-1.77"
                                        stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="m14.81 17.34 1.69-1.68-1.69-1.69" stroke="#292D32" stroke-width="1.5"
                                        stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z"
                                        stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                </symbol>
                            </use>
                        </svg><span class="label">外汇承兑</span></div><van-icon name="arrow" />
                </div> -->
                <div class="link-item" @click="getIsPass">
                    <div class="link-title"><svg class="svg-icon" aria-hidden="true">
                            <use xlink:href="#icon-password">
                                <symbol fill="none" viewBox="0 0 24 24" id="icon-password">
                                    <path
                                        d="M6 10V8c0-3.31 1-6 6-6s6 2.69 6 6v2M12 18.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"></path>
                                    <path d="M17 22H7c-4 0-5-1-5-5v-2c0-4 1-5 5-5h10c4 0 5 1 5 5v2c0 4-1 5-5 5Z"
                                        stroke="#292D32" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"></path>
                                </symbol>
                            </use>
                        </svg><span class="label">{{ $t('交易密码') }}</span></div><van-icon name="arrow" />
                </div>

                <div class="link-item" @click="shiming">
                    <div class="link-title">
                        <svg class="svg-icon" aria-hidden="true" viewBox="0 0 1024 1024" version="1.1" p-id="10915" ><path d="M298.688 298.688a213.312 213.312 0 1 1 426.624 0 213.312 213.312 0 0 1-426.624 0z m-149.376 512a213.312 213.312 0 0 1 213.376-213.376h298.624a213.312 213.312 0 0 1 213.376 213.376V896H149.312v-85.312z" p-id="10916"></path></svg>
                        <span class="label">{{ $t('实名认证') }}</span></div><van-icon name="arrow" />
                </div>

            </div>
        </div>

        <div class="record-wrap">
            <div class="record-title">{{ $t('资金明细') }}</div>
            <div class="record-list">
                <van-list v-model="loading" :finished="finished" :loading-text="$t('加载中...')" :finished-text="$t('没有更多了')"
                    @load="getList" offset="30">
                    <div class="record-item" @click="getDetail(item)" v-for="(item, index) in mingxiList" :key="index">
                        <div class="record-content">
                            <div class="label">{{ item.sourceType | filterType(that) }}</div>
                            <div class="time">{{ item.createTime }}</div>
                        </div><van-icon name="arrow" />
                    </div>
                </van-list>
            </div>
        </div>

        <van-popup v-model="withdrawalShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{ $t('提现') }}</div>

              <div class="filter-section">
                <div class="filter-label">
                  <label>{{ $t('提现方式') }}</label>
                </div>
                <div class="filter-field" @click="showFilterPopup = true">
                  <div class="filter-value">
                    <span>{{ getSelectedMethodLabel() || $t('选择提现方式') }}</span>
                  </div>
                  <van-icon name="arrow" class="arrow-icon"/>
                </div>
              </div>


                <div class="form-wrap">
                    <div class="label">{{ $t('请输入您的提现金额') }}</div>
                    <div class="input-wrap">
                        <van-field v-model="amount" label="$" type="number" :placeholder="$t('自定义金额')" clearable />
                    </div>
                    <div class="form-tips">
                        <span class="count-money">{{ $t('可用余额') }} ${{ walletDetail.availableFunds }}</span>
                        <span class="all-text" @click="amount = walletDetail.availableFunds">{{ $t('全部提现') }}</span>
                    </div>
                </div>
                <van-button round type="info" color="#0866ff" size="large" :disabled="amount ? false : true"
                    @click="getWithdrawal">{{ $t('提现') }}</van-button>
            </div>
        </van-popup>

        <van-popup v-model="rechargeShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{ $t('充值提示') }}</div>
                <img src="../assets/recharge-banner.png" alt="" width="343" class="banner">
                <p class="text">{{ $t('由于不同国家货币承兑问题') }}</p>
                <p class="text">{{ $t('当前只支持人工充值渠道') }}</p>
                <p class="text">{{ $t('请联系在线客服充值') }}</p>
                <van-button round type="info" color="#0866ff" size="large"
                    @click="getLink('customerService')">{{ $t('联系客服') }}</van-button>
            </div>
        </van-popup>

        <van-popup v-model="detailShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{ $t('明细详情') }}</div>
                <div class="content-wrap">
                    <div class="content-item"><span class="label">{{ $t('编号') }}</span><span class="text">{{ currutDetail.id }}</span>
                    </div>
                    <div class="content-item"><span class="label">{{ $t('时间') }}</span><span
                            class="text">{{ currutDetail.createTime }}</span>
                    </div>
                    <div class="content-item"><span class="label">{{ $t('金额') }}</span><span
                            class="text">${{ currutDetail.money }}</span></div>
                    <div class="content-item" v-if="currutDetail.sourceType == '2'">
                        <span class="label">{{ $t('提现状态') }}</span>
                        <span class="text">{{ currutDetail.withStatus | filterWithStatus(that) }}</span>
                    </div>
                    <div class="content-item"><span class="label">{{ $t('备注') }}</span><span class="text">{{ currutDetail.remarks ?
            currutDetail.remarks : $t('无备注') }}</span></div>
                </div>
            </div>
        </van-popup>

        <van-popup v-model="passShow" position="bottom">
            <div class="dialog-wrap" :style="showKeyboard == true ? 'padding-bottom: 310px;' : ''">
                <div class="dialog-title">{{ $t('交易密码') }}</div>
                <div class="desc">{{ $t('请输入您的交易密码') }}</div>
                <van-password-input :value="password" :length="6" :focused="showKeyboard"
                    @focus="showKeyboard = true" />
                <div class="btn-wrap">
                    <van-button type="primary" color="#0866ff" size="large"
                        :disabled="password.length != 6 ? true : false" @click="getPassVerify">{{ $t('确认') }}</van-button>
                </div>
                <van-number-keyboard v-model="password" :maxlength="6" :show="showKeyboard"
                    @blur="showKeyboard = false" />
            </div>
        </van-popup>

        <van-popup v-model="noPassShow" position="bottom" round>
            <div class="dialog-wrap">
                <div class="dialog-title">{{ $t('交易密码') }}</div>
                <img src="../assets/pass-banner.png" alt="" width="343" class="banner">
                <p class="text">{{ $t('处理交易密码相关问题') }}</p>
                <p class="text">{{ $t('请联系客服解决') }}</p>
                <van-button round type="info" color="#0866ff" size="large"
                    @click="getLink('customerService')">{{ $t('联系客服') }}</van-button>
            </div>
        </van-popup>

        <!-- 筛选弹窗 -->
        <van-popup v-model="showFilterPopup" position="bottom" round>
            <div class="filter-popup">
                <div class="popup-header">
                    <span class="popup-title">{{ $t('提现方式') }}</span>
                    <van-icon name="cross" @click="showFilterPopup = false" class="close-icon"/>
                </div>

                <div class="filter-options">
                    <div
                        v-for="method in availableWithdrawalMethods"
                        :key="method.key"
                        class="filter-option"
                        :class="{ active: selectedMethod === method.key }"
                        @click="selectMethod(method.key)"
                    >
                        <span>{{ method.label }}</span>
                        <van-icon v-if="selectedMethod === method.key" name="success" class="check-icon"/>
                    </div>
                </div>

                <div class="popup-buttons">
                    <van-button class="cancel-btn" @click="showFilterPopup = false">{{ $t('取消') }}</van-button>
                    <van-button type="primary" color="#1989fa" @click="confirmSelection">{{ $t('确认') }}</van-button>
                </div>
            </div>
        </van-popup>
    </div>

</template>
<script>
import { property, withdraw, getUserInfo, frontList } from '@/request/api'
export default {
    data() {
        return {
            isLock: true,
            loading: false,
            finished: false,
            page: 1,
            pageSize: 20,
            withdrawalShow: false,
            rechargeShow: false,
            amount: null,
            detailShow: false,
            walletDetail: "",
            passShow: false,
            password: "",
            showKeyboard: true,
            userInfo: {},
            noPassShow: false,
            mingxiList: [],
            currutDetail: {},
            that:this,
            showFilterPopup: false,
            selectedMethod: '',

        };
    },
    mounted() {
        this.getUserDetail()
        this.getWallet()
    },
    computed: {
        availableWithdrawalMethods() {
            const methods = [];

            if (this.userInfo.btcAdr) {
                methods.push({ key: 'BTC', label: 'BTC' });
            }
            if (this.userInfo.ethAdr) {
                methods.push({ key: 'ETH', label: 'ETH' });
            }
            if (this.userInfo.usdtAdr) {
                methods.push({ key: 'USDT', label: 'USDT' });
            }
            if (this.userInfo.usdcAdr) {
                methods.push({ key: 'USDC', label: 'USDC' });
            }
            if (this.userInfo.bankAccount) {
                methods.push({ key: 'BANK', label: 'BANK' });
            }

            return methods;
        }
    },
    watch: {
        password(val) {
            if (val.length == 6) {
                this.showKeyboard = false
            }
        },
    },
    filters: {
        filterType(val,that) {
            switch (val) {
                case '1':
                    return that.$t('系统充值')
                case '2':
                    return that.$t('人工提现')
                case '3':
                    return that.$t('广告投放')
                case '4':
                    return that.$t('系统扣款')
                case '5':
                    return that.$t('广告结算')
                case '6':
                    return that.$t('在线充值')
                case '7':
                    return that.$t('USDT提现')
                case '8':
                    return that.$t('USDT充值')
                case '9':
                    return that.$t('投放失败')
              case '10':
                return that.$t('开通会员')
              case '11':
                return that.$t('下级分佣')
              case '12':
                return that.$t('一次性奖励')
                default:
                    break;
            }
        },
        filterWithStatus(val, that) {
            switch (val) {
                case 0:
                case '0':
                    return that.$t('待审核')
                case 1:
                case '1':
                    return that.$t('提现成功')
                case 2:
                case '2':
                    return that.$t('提现驳回')
                case 3:
                case '3':
                    return that.$t('提现中')
                default:
                    return that.$t('待审核')
            }
        }
    },
    methods: {
        // 检查 绑定交易密码
        getIsPass() {
            if (!this.userInfo.password) {
                this.getLink('setPassword')
                return
            } else {
                this.noPassShow = true
            }
        },
        shiming(){
            this.getLink('Authentication')
        },
        // 绑定银行卡前 检查绑定交易密码
        getBindCard() {
            if (!this.userInfo.password) {
                this.getLink('setPassword')
                return
            }
            this.getLink('bindCard')
        },
        // 提现前检查 绑定交易密码 绑定银行卡
        getTixian() {
            if (!this.userInfo.password) {
                this.getLink('setPassword')
                return
            }
            if (!this.userInfo.bankAccount&&!this.userInfo.btcAdr&&!this.userInfo.ethAdr
              &&!this.userInfo.usdtAdr&&!this.userInfo.usdcAdr) {
                this.getLink('bindCard')
                return
            }
            this.withdrawalShow = true
        },
        // 用户信息
        getUserDetail() {
            const params = {};
            getUserInfo(params).then((res) => {
                if (res.code == 200) {
                    this.userInfo = res.data

                }
            });
        },
        // 资金信息
        getWallet() {
            const params = {};
            property(params).then((res) => {
                if (res.code == 200) {
                    this.walletDetail = res.data
                    this.walletDetail.balance = Number(this.walletDetail.availableFunds) + Number(this.walletDetail.toBeSettled) + Number(this.walletDetail.waitSpentAmount)
                }
            });
        },
        getDetail(item) {
            this.currutDetail = item
            this.detailShow = true
        },
        // 提现打开输入金额弹窗
        getWithdrawal() {

            if (!this.getSelectedMethodLabel()) {
              this.$toast(this.$t('请选择提现方式'))
              return
            }

            if (!this.amount || this.amount == 0) {
                this.$toast(this.$t('请输入提现金额'))
                return
            }
            if (Number(this.amount) > Number(this.walletDetail.availableFunds)) {
                this.$toast(this.$t('可用余额不足'))
                return
            }
            this.withdrawalShow = false
            this.passShow = true
        },
        // 提现打开输入密码弹窗
        getPassVerify() {
            const params = {
                money: this.amount,
                password: this.password,
                withType:this.selectedMethod,
                walletAdr:this.getWalletAdr(),
            };
            withdraw(params).then((res) => {
                if (res.code == 200) {
                    this.$toast.success(this.$t('提现成功'))
                    this.getWallet()
                    this.refreshList() // 刷新资金明细列表
                    this.passShow = false
                    // 清空表单数据
                    this.amount = null
                    this.password = ""
                } else {
                    this.$toast(res.msg)
                }
            })
        },

       getWalletAdr(){
          if (this.selectedMethod==="BTC"){
            return this.userInfo.btcAdr
          }else if (this.selectedMethod==="ETH"){
            return this.userInfo.ethAdr
          }else if (this.selectedMethod==="USDT"){
            return this.userInfo.usdtAdr
          }else if (this.selectedMethod==="USDC"){
            return this.userInfo.usdcAdr
          }else  {
            return this.userInfo.bankAccount
          }
       },
        onClickLeft() {
            this.$router.go(-1)
        },
        getLink(val) {
            this.$router.push({
                path: val
            })
        },
        getList() {
            this.loading = true;
            const params = {
                pageNum: this.page++,
                pageSize: this.pageSize,
            };
            frontList(params).then((res) => {
                if (res.code == 200) {
                    this.loading = false;
                    this.mingxiList = this.mingxiList.concat(res.rows);
                    if (this.mingxiList.length >= res.total) {
                        this.finished = true;
                    }
                }
            })
        },
        // 刷新资金明细列表
        refreshList() {
            // 重置分页状态
            this.page = 1;
            this.mingxiList = [];
            this.finished = false;
            this.loading = false;

            // 重新加载第一页数据
            this.getList();
        },
        selectMethod(method) {
            this.selectedMethod = method
        },
        confirmSelection() {
            this.showFilterPopup = false
            // 这里可以根据选择的方法做相应处理
        },
        getSelectedMethodLabel() {
            if (!this.selectedMethod) return '';
            const method = this.availableWithdrawalMethods.find(m => m.key === this.selectedMethod);
            return method ? method.label : this.selectedMethod;
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .section {
        padding: 5.12821vw 4.10256vw 0;

        .title-line {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title {
                flex: 1;
                display: flex;
                align-items: center;
                font-size: 5.12821vw;
                color: #000000;

                .svg-icon {
                    margin-left: 1.02564vw;
                }
            }

            .tag {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 13.84615vw;
                height: 5.12821vw;
                border-radius: 5.12821vw;
                background: #F0F2F5;
                font-size: 2.5641vw;
                color: #000000;
            }
        }

        .money {
            font-size: 8.97436vw;
            color: #000000;
            font-weight: 700;
            margin-top: 4.10256vw;
            line-height: 8.97436vw;
        }

        .money-content {
            margin-top: 7.69231vw;
            padding: 4.61538vw 6.15385vw 4.61538vw 3.07692vw;
            border-radius: 3.07692vw;
            background: #F0F2F5;

            .money-item {
                margin-bottom: 4.10256vw;
                display: flex;
                align-items: center;

                justify-content: space-between;
                font-size: 3.07692vw;
                color: #000000;

                .text {
                    flex: 1;
                    text-align: right;
                    margin-left: 2.5641vw;
                    flex-shrink: 0;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .btn-wrap {
            margin-top: 5.12821vw;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 5.12821vw;
            border-bottom: 1px solid #E5E5E5;

            /deep/ .van-button {
                width: 48%;
            }
        }

        .link-list {
            padding: 7.17949vw 0 6.15385vw;

            .link-item {
                margin-bottom: 5.64103vw;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #65676a;

                &:last-child {
                    margin-bottom: 0;
                }

                .link-title {
                    display: flex;
                    align-items: center;
                    font-size: 4.10256vw;
                    color: #000000;
                    flex: 1;
                    margin-right: 2.5641vw;

                    .svg-icon {
                        font-size: 6.15385vw;
                        margin-right: 1.53846vw;
                        color: #292d32;
                    }
                }

                .van-icon {
                    flex-shrink: 0;
                    margin-left: 3.58974vw;
                    font-size: 3.58974vw;
                    color: #65676a;
                }
            }
        }
    }

    .record-wrap {
        border-top: 2.05128vw solid #F0F2F5;
        padding: 6.15385vw 4.10256vw;

        .record-title {
            line-height: 1;
            font-size: 4.61538vw;
            color: #000000;
            font-weight: 500;
            padding-bottom: 5.12821vw;
            border-bottom: 1px solid #E5E5E5;
        }

        .record-list {
            .record-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 5.12821vw 0;
                border-bottom: 1px solid #E5E5E5;

                .record-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;
                    line-height: 1;

                    .label {
                        font-size: 4.10256vw;
                        color: #000000;
                        font-weight: 500;

                        .withdraw-status {
                            font-size: 3.58974vw;
                            color: #666666;
                            font-weight: 400;
                            margin-left: 2.56410vw;
                        }
                    }

                    .time {
                        margin-top: 3.07692vw;
                        font-size: 3.33333vw;
                        color: #000000;
                    }
                }

                .van-icon {
                    flex-shrink: 0;
                    margin-left: 3.58974vw;
                    font-size: 3.58974vw;
                    color: #65676a;
                }
            }
        }
    }

    .dialog-wrap {
        padding: 10.25641vw 4.10256vw 4.10256vw;
        background: #ffffff;

        .dialog-title {
            text-align: center;
            font-weight: 700;
            font-size: 6.41026vw;
            margin-bottom: 12.82051vw;
        }

        .desc {
            text-align: center;
            font-size: 3.84615vw;
            color: #666666;
            margin-bottom: 7.69231vw;
        }

        .form-wrap {
            .label {
                font-size: 3.84615vw;
                color: #666666;
            }

            .input-wrap {
                display: flex;
                align-items: center;
                height: 16.92308vw;
                border-bottom: 1px solid #E5E5E5;

                /deep/ .van-cell {
                    flex: 1;
                    padding: 0;
                    font-size: 5.64103vw;
                    color: #000000;
                    font-weight: 700;

                    .van-field__label {
                        width: auto;
                        color: #000000;
                    }
                }
            }

            .form-tips {
                display: flex;
                align-items: center;
                margin-top: 3.84615vw;
                font-size: 3.07692vw;

                .count-money {
                    display: flex;
                    align-items: center;
                    margin-right: 2.5641vw;
                    color: #000000;
                }

                .all-text {
                    color: #0866ff;
                    cursor: pointer;
                }
            }
        }

        .text {
            font-size: 4.61538vw;
            font-weight: 500;
            color: #000000;
            text-align: center;
            padding: 0 4.10256vw;
        }

        /deep/ .van-button {
            margin-top: 4.61538vw;
            border-radius: 16px;
        }

        .content-wrap {
            margin-top: 12.82051vw;

            .content-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 4.35897vw;
                margin-bottom: 8.71795vw;

                .label {
                    color: #666666;
                    min-width: 20.51282vw;
                }

                .text {
                    flex: 1;
                    flex-shrink: 0;
                    margin-left: 2.5641vw;
                    text-align: right;
                    color: #000000;
                    font-weight: 500;
                    word-break: break-all;
                }
            }
        }
    }

    .filter-section {
        display: flex;
        align-items: center;
        margin-bottom: 8.20513vw;
        padding-bottom: 8.20513vw;
        border-bottom: 1px solid #E5E5E5;

        .filter-label {
            width: 25.64103vw;
            margin-right: 4.10256vw;

            label {
                font-size: 3.73333vw;
                color: #000000;
                line-height: 1.2;
                white-space: normal;
                word-break: break-word;
                text-align: left;
                display: block;
            }
        }

        .filter-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;
            cursor: pointer;

            .filter-value {
                flex: 1;

                span {
                    font-size: 3.58974vw;
                    color: #969799;
                    line-height: 1;
                }
            }

            .arrow-icon {
                font-size: 4.61538vw;
                color: #C8C9CC;
                margin-left: 2.56410vw;
            }
        }
    }

    .filter-popup {
        padding: 6.41026vw 4.10256vw 4.10256vw;
        background: #ffffff;
        border-radius: 20px 20px 0 0;

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6.41026vw;

            .popup-title {
                font-size: 5.12821vw;
                font-weight: 600;
                color: #323233;
            }

            .close-icon {
                font-size: 5.12821vw;
                color: #969799;
            }
        }

        .filter-options {
            margin-bottom: 8.20513vw;

            .filter-option {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4.10256vw;
                background-color: #f7f8fa;
                border-radius: 12px;
                margin-bottom: 3.07692vw;

                span {
                    font-size: 4.10256vw;
                    color: #646566;
                }

                .check-icon {
                    font-size: 4.61538vw;
                    color: #1989fa;
                }

                &.active {
                    background-color: #f7f8fa;

                    span {
                        color: #323233;
                    }
                }
            }
        }

        .popup-buttons {
            display: flex;
            gap: 3.07692vw;

            .cancel-btn {
                flex: 1;
                background-color: #ffffff;
                color: #646566;
                border: 1px solid #ebedf0;
                border-radius: 6px;
                height: 11.53846vw;
                font-size: 4.10256vw;
            }

            .van-button--primary {
                flex: 1;
                border-radius: 6px;
                height: 11.53846vw;
                font-size: 4.10256vw;
            }
        }
    }
}
</style>
