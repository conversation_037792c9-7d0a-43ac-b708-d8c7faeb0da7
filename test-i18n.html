<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>i18n Test</title>
</head>
<body>
    <h1>i18n 配置测试</h1>
    
    <div>
        <h2>测试阿拉伯语配置</h2>
        <p>在浏览器控制台中运行以下代码来测试：</p>
        <pre>
// 测试设置阿拉伯语
localStorage.setItem('language', 'ar_SA');
location.reload();

// 或者使用语言工具类
import { LanguageUtils } from './src/utils/language.js';
LanguageUtils.setLanguage('ar_SA');
        </pre>
    </div>

    <div>
        <h2>当前支持的语言：</h2>
        <ul>
            <li>zh_CN - 简体中文</li>
            <li>zh_TW - 繁体中文</li>
            <li>en_US - 英语</li>
            <li>ar_SA - 阿拉伯语 (新增)</li>
        </ul>
    </div>

    <div>
        <h2>测试翻译：</h2>
        <p>广告中心 应该显示为：</p>
        <ul>
            <li>简体中文: 广告中心</li>
            <li>繁体中文: 廣告中心</li>
            <li>英语: Ad Center</li>
            <li>阿拉伯语: مركز الإعلانات</li>
        </ul>
    </div>
</body>
</html>
