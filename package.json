{"name": "Facebook", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"amfe-flexible": "^2.2.1", "animate.css": "^4.1.1", "axios": "^1.6.5", "core-js": "^3.8.3", "dplayer": "^1.27.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "qs": "^6.11.2", "swiper": "^11.1.3", "vant": "^2.13.2", "vconsole": "^3.15.1", "vue": "^2.6.14", "vue-i18n": "^8.23.0", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^6.1.0", "babel-plugin-import": "^1.13.8", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "postcss-pxtorem": "^6.1.0", "vue-template-compiler": "^2.6.14"}}