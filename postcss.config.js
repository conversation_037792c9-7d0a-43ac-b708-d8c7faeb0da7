module.exports = {
    plugins: [
        require('postcss-pxtorem')({
            rootValue({ file }) {
                return file.indexOf('vant') !== -1 ? 37.5 : 37.5
            }, // 设置合适的 rootValue 值
            unitPrecision: 5, // 单位转换后的小数位数
            propList: ['*'], // 需要进行单位转换的属性列表
            selectorBlackList: [], // 需要忽略的选择器列表
            replace: true, // 是否替换原始的属性值
            mediaQuery: false, // 是否在媒体查询中也进行单位转换
            minPixelValue: 1 // 小于等于 1px 的值不进行单位转换
        })
    ]
}